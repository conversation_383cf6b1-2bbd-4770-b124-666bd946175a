{"execution_time": 3.079463481903076, "tree_structure": {"root": {"node_type": "branch", "is_leaf": false, "statistics": {"sample_size": 800, "node_entropy": 0.8072916195433455, "node_depth": 0, "class_distribution": {"Yes": 602, "No": 198}, "gain_ratio": 0.18196921050590262}, "feature": {"name": "income", "data_type": "numeric", "threshold": 20097.0}, "majority_class": "Yes", "branches": {"<=": {"node_type": "leaf", "is_leaf": true, "statistics": {"sample_size": 1, "node_entropy": 0.0, "node_depth": 1, "class_distribution": {"No": 1}}, "prediction": "No", "class": "No"}, ">": {"node_type": "branch", "is_leaf": false, "statistics": {"sample_size": 799, "node_entropy": 0.8057772826122737, "node_depth": 1, "class_distribution": {"Yes": 602, "No": 197}, "gain_ratio": 0.18249667181446896}, "feature": {"name": "income", "data_type": "numeric", "threshold": 20121.5}, "majority_class": "Yes", "branches": {"<=": {"node_type": "leaf", "is_leaf": true, "statistics": {"sample_size": 1, "node_entropy": 0.0, "node_depth": 2, "class_distribution": {"No": 1}}, "prediction": "No", "class": "No"}, ">": {"node_type": "branch", "is_leaf": false, "statistics": {"sample_size": 798, "node_entropy": 0.8042522359016422, "node_depth": 2, "class_distribution": {"Yes": 602, "No": 196}, "gain_ratio": 0.20147656354842564}, "feature": {"name": "customer_id", "data_type": "numeric", "threshold": 998927.5}, "majority_class": "Yes", "branches": {"<=": {"node_type": "branch", "is_leaf": false, "statistics": {"sample_size": 796, "node_entropy": 0.8011696748229913, "node_depth": 3, "class_distribution": {"Yes": 602, "No": 194}, "gain_ratio": 0.18409946103949154}, "feature": {"name": "income", "data_type": "numeric", "threshold": 20180.5}, "majority_class": "Yes", "branches": {"<=": {"node_type": "leaf", "is_leaf": true, "statistics": {"sample_size": 1, "node_entropy": 0.0, "node_depth": 4, "class_distribution": {"No": 1}}, "prediction": "No", "class": "No"}, ">": {"node_type": "branch", "is_leaf": false, "statistics": {"sample_size": 795, "node_entropy": 0.7996119890370257, "node_depth": 4, "class_distribution": {"Yes": 602, "No": 193}, "gain_ratio": 0.18464064825506896}, "feature": {"name": "income", "data_type": "numeric", "threshold": 20268.5}, "majority_class": "Yes", "branches": {"<=": {"node_type": "leaf", "is_leaf": true, "statistics": {"sample_size": 1, "node_entropy": 0.0, "node_depth": 5, "class_distribution": {"No": 1}}, "prediction": "No", "class": "No"}, ">": {"node_type": "branch", "is_leaf": false, "statistics": {"sample_size": 794, "node_entropy": 0.7980432506092013, "node_depth": 5, "class_distribution": {"Yes": 602, "No": 192}, "gain_ratio": 0.1465600619199895}, "feature": {"name": "is_premium", "data_type": "numeric", "threshold": 0.5}, "majority_class": "Yes", "branches": {"<=": {"node_type": "branch", "is_leaf": false, "statistics": {"sample_size": 373, "node_entropy": 0.9894752857719396, "node_depth": 6, "class_distribution": {"Yes": 209, "No": 164}, "gain_ratio": 0.19023298860750823}, "feature": {"name": "income", "data_type": "numeric", "threshold": 75568.5}, "majority_class": "Yes", "branches": {"<=": {"node_type": "branch", "is_leaf": false, "statistics": {"sample_size": 170, "node_entropy": 0.8664307357149303, "node_depth": 7, "class_distribution": {"No": 121, "Yes": 49}, "gain_ratio": 0.2189697759733493}, "feature": {"name": "education", "data_type": "nominal"}, "majority_class": "No", "branches": {"PhD": {"node_type": "leaf", "is_leaf": true, "statistics": {"sample_size": 42, "node_entropy": 0.9852281360342515, "node_depth": 8, "class_distribution": {"No": 18, "Yes": 24}, "gain_ratio": 0.8562682786739844}, "prediction": "Yes", "class": "Yes"}, "High_School": {"node_type": "leaf", "is_leaf": true, "statistics": {"sample_size": 40, "node_entropy": 0.0, "node_depth": 8, "class_distribution": {"No": 40}}, "prediction": "No", "class": "No"}, "Bachelor": {"node_type": "leaf", "is_leaf": true, "statistics": {"sample_size": 52, "node_entropy": 0.0, "node_depth": 8, "class_distribution": {"No": 52}}, "prediction": "No", "class": "No"}, "Master": {"node_type": "leaf", "is_leaf": true, "statistics": {"sample_size": 36, "node_entropy": 0.8879763195151349, "node_depth": 8, "class_distribution": {"No": 11, "Yes": 25}, "gain_ratio": 1.0}, "prediction": "Yes", "class": "Yes"}}}, ">": {"node_type": "branch", "is_leaf": false, "statistics": {"sample_size": 203, "node_entropy": 0.7449522736671061, "node_depth": 7, "class_distribution": {"Yes": 160, "No": 43}, "gain_ratio": 0.4176946165941081}, "feature": {"name": "age", "data_type": "numeric", "threshold": 40.5}, "majority_class": "Yes", "branches": {"<=": {"node_type": "branch", "is_leaf": false, "statistics": {"sample_size": 73, "node_entropy": 0.9770012394218561, "node_depth": 8, "class_distribution": {"Yes": 30, "No": 43}, "gain_ratio": 0.4947013691681751}, "feature": {"name": "education", "data_type": "nominal"}, "majority_class": "No", "branches": {"PhD": {"node_type": "leaf", "is_leaf": true, "statistics": {"sample_size": 15, "node_entropy": 0.0, "node_depth": 9, "class_distribution": {"Yes": 15}}, "prediction": "Yes", "class": "Yes"}, "High_School": {"node_type": "leaf", "is_leaf": true, "statistics": {"sample_size": 20, "node_entropy": 0.0, "node_depth": 9, "class_distribution": {"No": 20}}, "prediction": "No", "class": "No"}, "Bachelor": {"node_type": "leaf", "is_leaf": true, "statistics": {"sample_size": 23, "node_entropy": 0.0, "node_depth": 9, "class_distribution": {"No": 23}}, "prediction": "No", "class": "No"}, "Master": {"node_type": "leaf", "is_leaf": true, "statistics": {"sample_size": 15, "node_entropy": 0.0, "node_depth": 9, "class_distribution": {"Yes": 15}}, "prediction": "Yes", "class": "Yes"}}}, ">": {"node_type": "leaf", "is_leaf": true, "statistics": {"sample_size": 130, "node_entropy": 0.0, "node_depth": 8, "class_distribution": {"Yes": 130}}, "prediction": "Yes", "class": "Yes"}}}}}, ">": {"node_type": "branch", "is_leaf": false, "statistics": {"sample_size": 421, "node_entropy": 0.3527561375432482, "node_depth": 6, "class_distribution": {"Yes": 393, "No": 28}, "gain_ratio": 0.12881073674718907}, "feature": {"name": "age", "data_type": "numeric", "threshold": 40.5}, "majority_class": "Yes", "branches": {"<=": {"node_type": "branch", "is_leaf": false, "statistics": {"sample_size": 135, "node_entropy": 0.7364977795505668, "node_depth": 7, "class_distribution": {"Yes": 107, "No": 28}, "gain_ratio": 0.35678226062506624}, "feature": {"name": "income", "data_type": "numeric", "threshold": 73267.5}, "majority_class": "Yes", "branches": {"<=": {"node_type": "branch", "is_leaf": false, "statistics": {"sample_size": 53, "node_entropy": 0.9976875760352552, "node_depth": 8, "class_distribution": {"No": 28, "Yes": 25}, "gain_ratio": 0.5048563692307002}, "feature": {"name": "education", "data_type": "nominal"}, "majority_class": "No", "branches": {"PhD": {"node_type": "leaf", "is_leaf": true, "statistics": {"sample_size": 15, "node_entropy": 0.0, "node_depth": 9, "class_distribution": {"Yes": 15}}, "prediction": "Yes", "class": "Yes"}, "High_School": {"node_type": "leaf", "is_leaf": true, "statistics": {"sample_size": 16, "node_entropy": 0.0, "node_depth": 9, "class_distribution": {"No": 16}}, "prediction": "No", "class": "No"}, "Bachelor": {"node_type": "leaf", "is_leaf": true, "statistics": {"sample_size": 12, "node_entropy": 0.0, "node_depth": 9, "class_distribution": {"No": 12}}, "prediction": "No", "class": "No"}, "Master": {"node_type": "leaf", "is_leaf": true, "statistics": {"sample_size": 10, "node_entropy": 0.0, "node_depth": 9, "class_distribution": {"Yes": 10}}, "prediction": "Yes", "class": "Yes"}}}, ">": {"node_type": "leaf", "is_leaf": true, "statistics": {"sample_size": 82, "node_entropy": 0.0, "node_depth": 8, "class_distribution": {"Yes": 82}}, "prediction": "Yes", "class": "Yes"}}}, ">": {"node_type": "leaf", "is_leaf": true, "statistics": {"sample_size": 286, "node_entropy": 0.0, "node_depth": 7, "class_distribution": {"Yes": 286}}, "prediction": "Yes", "class": "Yes"}}}}}}}}}, ">": {"node_type": "leaf", "is_leaf": true, "statistics": {"sample_size": 2, "node_entropy": 0.0, "node_depth": 3, "class_distribution": {"No": 2}}, "prediction": "No", "class": "No"}}}}}}}, "metadata": {"algorithm": "C4.5", "implementation": "Python", "features_count": 2}}, "metadata": {"training_samples": 800, "features_count": 26, "target_classes": ["Yes", "No"]}}