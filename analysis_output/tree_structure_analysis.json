{"python_analysis": {"implementation": "Python", "total_nodes": 33, "leaf_nodes": 20, "decision_nodes": 13, "max_depth": 9, "features_used": {"income": 6, "customer_id": 1, "is_premium": 1, "education": 3, "age": 2}, "split_thresholds": {"income": [20097.0, 20121.5, 20180.5, 20268.5, 75568.5, 73267.5], "customer_id": [998927.5], "is_premium": [0.5], "age": [40.5, 40.5]}, "node_sample_sizes": [800, 1, 799, 1, 798, 796, 1, 795, 1, 794, 373, 170, 42, 40, 52, 36, 203, 73, 15, 20, 23, 15, 130, 421, 135, 53, 15, 16, 12, 10, 82, 286, 2], "leaf_predictions": {"No": 11, "Yes": 9}, "decision_paths": [{"path": "root.<=", "depth": 1, "prediction": "No", "samples": 1, "confidence": 0}, {"path": "root.>.<=", "depth": 2, "prediction": "No", "samples": 1, "confidence": 0}, {"path": "root.>.>.<=.<=", "depth": 4, "prediction": "No", "samples": 1, "confidence": 0}, {"path": "root.>.>.<=.>.<=", "depth": 5, "prediction": "No", "samples": 1, "confidence": 0}, {"path": "root.>.>.<=.>.>.<=.<=.PhD", "depth": 8, "prediction": "Yes", "samples": 42, "confidence": 0}, {"path": "root.>.>.<=.>.>.<=.<=.High_School", "depth": 8, "prediction": "No", "samples": 40, "confidence": 0}, {"path": "root.>.>.<=.>.>.<=.<=.Bachelor", "depth": 8, "prediction": "No", "samples": 52, "confidence": 0}, {"path": "root.>.>.<=.>.>.<=.<=.Master", "depth": 8, "prediction": "Yes", "samples": 36, "confidence": 0}, {"path": "root.>.>.<=.>.>.<=.>.<=.PhD", "depth": 9, "prediction": "Yes", "samples": 15, "confidence": 0}, {"path": "root.>.>.<=.>.>.<=.>.<=.High_School", "depth": 9, "prediction": "No", "samples": 20, "confidence": 0}, {"path": "root.>.>.<=.>.>.<=.>.<=.Bachelor", "depth": 9, "prediction": "No", "samples": 23, "confidence": 0}, {"path": "root.>.>.<=.>.>.<=.>.<=.Master", "depth": 9, "prediction": "Yes", "samples": 15, "confidence": 0}, {"path": "root.>.>.<=.>.>.<=.>.>", "depth": 8, "prediction": "Yes", "samples": 130, "confidence": 0}, {"path": "root.>.>.<=.>.>.>.<=.<=.PhD", "depth": 9, "prediction": "Yes", "samples": 15, "confidence": 0}, {"path": "root.>.>.<=.>.>.>.<=.<=.High_School", "depth": 9, "prediction": "No", "samples": 16, "confidence": 0}, {"path": "root.>.>.<=.>.>.>.<=.<=.Bachelor", "depth": 9, "prediction": "No", "samples": 12, "confidence": 0}, {"path": "root.>.>.<=.>.>.>.<=.<=.Master", "depth": 9, "prediction": "Yes", "samples": 10, "confidence": 0}, {"path": "root.>.>.<=.>.>.>.<=.>", "depth": 8, "prediction": "Yes", "samples": 82, "confidence": 0}, {"path": "root.>.>.<=.>.>.>.>", "depth": 7, "prediction": "Yes", "samples": 286, "confidence": 0}, {"path": "root.>.>.>", "depth": 3, "prediction": "No", "samples": 2, "confidence": 0}], "depth_distribution": {"0": 1, "1": 2, "2": 2, "3": 2, "4": 2, "5": 2, "6": 2, "7": 4, "8": 8, "9": 8}, "feature_importance_by_depth": {"0": {"income": 1}, "1": {"income": 1}, "2": {"customer_id": 1}, "3": {"income": 1}, "4": {"income": 1}, "5": {"is_premium": 1}, "6": {"income": 1, "age": 1}, "7": {"education": 1, "age": 1, "income": 1}, "8": {"education": 2}}}, "go_analysis": {"implementation": "Go", "total_nodes": 17, "leaf_nodes": 6, "decision_nodes": 11, "max_depth": 5, "features_used": {"income": 3, "customer_id": 1, "is_premium": 1, "age": 3, "education": 3}, "split_thresholds": {"income": [20268.5, 75017.5, 72163.5], "customer_id": [998927.5], "is_premium": [0.5], "age": [40.5, 40.5, 40.5], "education": [0, 0, 0]}, "node_sample_sizes": [800, 4, 796, 794, 373, 167, 55, 112, 206, 75, 131, 421, 135, 52, 83, 286, 2], "leaf_predictions": {"No": 3, "Yes": 3}, "decision_paths": [{"path": "root.left", "depth": 1, "prediction": "No", "samples": 4, "confidence": 1}, {"path": "root.right.left.left.left.left", "depth": 5, "prediction": "No", "samples": 55, "confidence": 1}, {"path": "root.right.left.left.right.right", "depth": 5, "prediction": "Yes", "samples": 131, "confidence": 1}, {"path": "root.right.left.right.left.right", "depth": 5, "prediction": "Yes", "samples": 83, "confidence": 1}, {"path": "root.right.left.right.right", "depth": 4, "prediction": "Yes", "samples": 286, "confidence": 1}, {"path": "root.right.right", "depth": 2, "prediction": "No", "samples": 2, "confidence": 1}], "depth_distribution": {"0": 1, "1": 2, "2": 2, "3": 2, "4": 4, "5": 6}, "feature_importance_by_depth": {"0": {"income": 1}, "1": {"customer_id": 1}, "2": {"is_premium": 1}, "3": {"income": 1, "age": 1}, "4": {"age": 2, "income": 1}, "5": {"education": 3}}}, "comparison_report": {"tree_statistics": {"python": {"total_nodes": 33, "leaf_nodes": 20, "decision_nodes": 13, "max_depth": 9, "avg_samples_per_node": 212.42424242424244}, "go": {"total_nodes": 17, "leaf_nodes": 6, "decision_nodes": 11, "max_depth": 5, "avg_samples_per_node": 264.2352941176471}}, "decision_paths": {"path_count": {"python": 20, "go": 6}, "prediction_distribution": {"python": {"No": 11, "Yes": 9}, "go": {"No": 3, "Yes": 3}}, "depth_analysis": {"python_depths": [1, 2, 4, 5, 8, 8, 8, 8, 9, 9, 9, 9, 8, 9, 9, 9, 9, 8, 7, 3], "go_depths": [1, 5, 5, 5, 4, 2]}, "avg_leaf_depth": {"python": 7.1, "go": 3.6666666666666665}}, "feature_analysis": {"common_features": ["age", "education", "income", "is_premium", "customer_id"], "python_only": [], "go_only": [], "feature_usage_frequency": {"python": {"income": 6, "customer_id": 1, "is_premium": 1, "education": 3, "age": 2}, "go": {"income": 3, "customer_id": 1, "is_premium": 1, "age": 3, "education": 3}}, "split_threshold_comparison": {"age": {"python_thresholds": [40.5, 40.5], "go_thresholds": [40.5, 40.5, 40.5], "threshold_match": true}, "education": {"python_thresholds": [], "go_thresholds": [0, 0, 0], "threshold_match": false}, "income": {"python_thresholds": [20097.0, 20121.5, 20180.5, 20268.5, 75568.5, 73267.5], "go_thresholds": [20268.5, 75017.5, 72163.5], "threshold_match": false}, "is_premium": {"python_thresholds": [0.5], "go_thresholds": [0.5], "threshold_match": true}, "customer_id": {"python_thresholds": [998927.5], "go_thresholds": [998927.5], "threshold_match": true}}}, "depth_distribution": {"python": {"0": 1, "1": 2, "2": 2, "3": 2, "4": 2, "5": 2, "6": 2, "7": 4, "8": 8, "9": 8}, "go": {"0": 1, "1": 2, "2": 2, "3": 2, "4": 4, "5": 6}}, "structural_equivalence": {"same_node_count": false, "same_leaf_count": false, "same_max_depth": false, "same_predictions": false}}}