{"python": {"error": "2025-07-31 21:57:30,053 - ERROR - Error loading configuration file: [Errno 2] No such file or directory: 'benchmark.yaml'\n", "execution_time": 0.5467548370361328}, "go": {"stdout": "\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:main:39 | Mulberri application starting\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:80 | Starting training workflow with input: testdata/testdata_train.csv, target: y, output: /tmp/tmpvh7h_ohe.dt\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:StartStage:394 | Starting stage: Loading Data\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|csv_loader.go:ReadTrainingCSV:159 | Starting CSV read operation: file=testdata/testdata_train.csv, target=y\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|csv_loader.go:ReadTrainingCSV:179 | Opening CSV file: testdata/testdata_train.csv\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|csv_loader.go:ReadTrainingCSV:202 | File size: 157258 bytes\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|csv_loader.go:ReadTrainingCSV:220 | Reading CSV headers\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|csv_loader.go:ReadTrainingCSV:241 | Found 27 headers: [age income customer_id height_cm weight_kg score_0_1 score_0_100 price_dollars color department city country zip_code is_premium has_subscription owns_car gender employment_status education satisfaction_rating shirt_size income_bracket performance_grade signup_date last_login preferred_time y]\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|csv_loader.go:ReadTrainingCSV:256 | Looking for target column: y\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|csv_loader.go:ReadTrainingCSV:274 | Target column 'y' found at index 26\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|csv_loader.go:ReadTrainingCSV:283 | Starting to read data rows\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|csv_loader.go:ReadTrainingCSV:317 | Successfully read 800 data rows\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|csv_loader.go:ReadTrainingCSV:329 | Successfully loaded CSV: 800 rows, 27 columns, target='y'\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|main.go:handleTrainCommand:91 | Loaded 800 rows with 27 columns\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:CompleteStage:410 | Completed stage: Loading Data (elapsed: 1ms)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:StartStage:394 | Starting stage: Processing Features\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|csv_loader.go:ExtractRecordsAndTarget:346 | Extracting features and targets: 800 rows, target='y' at index 26\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|csv_loader.go:ExtractRecordsAndTarget:375 | Created 26 feature headers: [age income customer_id height_cm weight_kg score_0_1 score_0_100 price_dollars color department city country zip_code is_premium has_subscription owns_car gender employment_status education satisfaction_rating shirt_size income_bracket performance_grade signup_date last_login preferred_time]\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|csv_loader.go:ExtractRecordsAndTarget:404 | Successfully extracted 800 records with 26 features each\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:createFeatureObjects:300 | No feature info file provided, using automatic feature detection\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:238 | Creating features: 26 headers, 800 rows\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'age' at index 0\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'age' (type: numeric)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'income' at index 1\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'income' (type: numeric)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'customer_id' at index 2\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'customer_id' (type: numeric)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'height_cm' at index 3\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'height_cm' (type: numeric)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'weight_kg' at index 4\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'weight_kg' (type: numeric)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'score_0_1' at index 5\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'score_0_1' (type: numeric)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'score_0_100' at index 6\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'score_0_100' (type: numeric)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'price_dollars' at index 7\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'price_dollars' (type: numeric)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'color' at index 8\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'color' (type: categorical)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'department' at index 9\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'department' (type: categorical)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'city' at index 10\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'city' (type: categorical)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'country' at index 11\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'country' (type: categorical)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'zip_code' at index 12\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'zip_code' (type: numeric)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'is_premium' at index 13\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'is_premium' (type: numeric)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'has_subscription' at index 14\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'has_subscription' (type: categorical)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'owns_car' at index 15\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'owns_car' (type: categorical)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'gender' at index 16\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'gender' (type: categorical)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'employment_status' at index 17\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'employment_status' (type: categorical)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'education' at index 18\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'education' (type: categorical)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'satisfaction_rating' at index 19\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'satisfaction_rating' (type: numeric)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'shirt_size' at index 20\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'shirt_size' (type: categorical)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'income_bracket' at index 21\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'income_bracket' (type: categorical)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'performance_grade' at index 22\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'performance_grade' (type: categorical)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'signup_date' at index 23\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'signup_date' (type: categorical)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'last_login' at index 24\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'last_login' (type: categorical)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'preferred_time' at index 25\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'preferred_time' (type: categorical)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|feature_creation.go:CreateFeaturesWithConfig:287 | Successfully created 26 features\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|main.go:handleTrainCommand:109 | Created 26 features from 800 samples\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature age: type=numeric, column=0\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature income: type=numeric, column=1\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature customer_id: type=numeric, column=2\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature height_cm: type=numeric, column=3\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature weight_kg: type=numeric, column=4\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature score_0_1: type=numeric, column=5\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature score_0_100: type=numeric, column=6\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature price_dollars: type=numeric, column=7\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature color: type=categorical, column=8\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature department: type=categorical, column=9\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature city: type=categorical, column=10\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature country: type=categorical, column=11\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature zip_code: type=numeric, column=12\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature is_premium: type=numeric, column=13\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature has_subscription: type=categorical, column=14\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature owns_car: type=categorical, column=15\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature gender: type=categorical, column=16\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature employment_status: type=categorical, column=17\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature education: type=categorical, column=18\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature satisfaction_rating: type=numeric, column=19\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature shirt_size: type=categorical, column=20\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature income_bracket: type=categorical, column=21\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature performance_grade: type=categorical, column=22\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature signup_date: type=categorical, column=23\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature last_login: type=categorical, column=24\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature preferred_time: type=categorical, column=25\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:UpdateProgress:402 | Processing Features: Created 26 features (elapsed: 7ms)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:CompleteStage:410 | Completed stage: Processing Features (elapsed: 7ms)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:StartStage:394 | Starting stage: Building Tree\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|dataset.go:NewCSVDatasetSmart:203 | No datetime features found, using basic CSV dataset\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|main.go:handleTrainCommand:124 | Created dataset with 800 samples\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|main.go:createAndBuildTree:195 | Creating splitter with criterion: entropy, min_samples_split: 2, min_samples_leaf: 1\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|main.go:createAndBuildTree:212 | Creating tree builder with max_depth: 10\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|main.go:createAndBuildTree:227 | Starting tree building process\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|tree_builder.go:BuildTree:429 | Starting tree construction with 800 samples and 26 features\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | agenumeric0\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='age', type=numeric\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'age' at index 0\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | incomenumeric1\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='income', type=numeric\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'income' at index 1\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | customer_idnumeric2\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='customer_id', type=numeric\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'customer_id' at index 2\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | height_cmnumeric3\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='height_cm', type=numeric\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'height_cm' at index 3\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | weight_kgnumeric4\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='weight_kg', type=numeric\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'weight_kg' at index 4\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | score_0_1numeric5\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='score_0_1', type=numeric\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'score_0_1' at index 5\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | score_0_100numeric6\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='score_0_100', type=numeric\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'score_0_100' at index 6\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | price_dollarsnumeric7\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='price_dollars', type=numeric\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'price_dollars' at index 7\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | colorcategorical8\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='color', type=categorical\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'color' at index 8\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | departmentcategorical9\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='department', type=categorical\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'department' at index 9\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | citycategorical10\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='city', type=categorical\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'city' at index 10\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | countrycategorical11\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='country', type=categorical\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'country' at index 11\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | zip_codenumeric12\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='zip_code', type=numeric\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'zip_code' at index 12\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | is_premiumnumeric13\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='is_premium', type=numeric\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'is_premium' at index 13\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | has_subscriptioncategorical14\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='has_subscription', type=categorical\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'has_subscription' at index 14\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | owns_carcategorical15\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='owns_car', type=categorical\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'owns_car' at index 15\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | gendercategorical16\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='gender', type=categorical\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'gender' at index 16\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | employment_statuscategorical17\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='employment_status', type=categorical\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'employment_status' at index 17\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | educationcategorical18\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='education', type=categorical\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'education' at index 18\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | satisfaction_ratingnumeric19\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='satisfaction_rating', type=numeric\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'satisfaction_rating' at index 19\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | shirt_sizecategorical20\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='shirt_size', type=categorical\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'shirt_size' at index 20\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | income_bracketcategorical21\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='income_bracket', type=categorical\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'income_bracket' at index 21\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | performance_gradecategorical22\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='performance_grade', type=categorical\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'performance_grade' at index 22\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | signup_datecategorical23\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='signup_date', type=categorical\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'signup_date' at index 23\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | last_logincategorical24\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='last_login', type=categorical\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'last_login' at index 24\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | preferred_timecategorical25\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='preferred_time', type=categorical\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'preferred_time' at index 25\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:buildNode:630 | Building node root at depth 0 with 800 samples\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:84 | Starting split evaluation: 800 samples, 26 features\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:117 | Base impurity calculated: 0.807292\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:135 | Best split found: feature=income, gain_ratio=0.223007\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:buildNode:630 | Building node root_L at depth 1 with 4 samples\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:buildNode:630 | Building node root_R at depth 1 with 796 samples\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:84 | Starting split evaluation: 796 samples, 26 features\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:117 | Base impurity calculated: 0.801170\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:135 | Best split found: feature=customer_id, gain_ratio=0.202665\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:buildNode:630 | Building node root_R_L at depth 2 with 794 samples\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:84 | Starting split evaluation: 794 samples, 26 features\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:117 | Base impurity calculated: 0.798043\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:135 | Best split found: feature=is_premium, gain_ratio=0.146560\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:84 | Starting split evaluation: 373 samples, 26 features\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:117 | Base impurity calculated: 0.989475\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:135 | Best split found: feature=income, gain_ratio=0.193863\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:84 | Starting split evaluation: 167 samples, 26 features\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:117 | Base impurity calculated: 0.857402\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:135 | Best split found: feature=age, gain_ratio=0.217985\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:84 | Starting split evaluation: 112 samples, 26 features\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:117 | Base impurity calculated: 0.981287\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:135 | Best split found: feature=education, gain_ratio=0.501001\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:84 | Starting split evaluation: 206 samples, 26 features\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:117 | Base impurity calculated: 0.748293\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:135 | Best split found: feature=age, gain_ratio=0.414524\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:84 | Starting split evaluation: 75 samples, 26 features\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:117 | Base impurity calculated: 0.978218\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:135 | Best split found: feature=education, gain_ratio=0.495449\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:84 | Starting split evaluation: 421 samples, 26 features\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:117 | Base impurity calculated: 0.352756\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:135 | Best split found: feature=age, gain_ratio=0.128811\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:84 | Starting split evaluation: 135 samples, 26 features\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:117 | Base impurity calculated: 0.736498\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:135 | Best split found: feature=income, gain_ratio=0.367045\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:84 | Starting split evaluation: 52 samples, 26 features\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:117 | Base impurity calculated: 0.995727\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:135 | Best split found: feature=education, gain_ratio=0.503278\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:buildNode:630 | Building node root_R_R at depth 2 with 2 samples\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:UpdateStatistics:578 | Updating tree statistics\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:UpdateStatistics:584 | Tree statistics updated: 29 nodes, 18 leaves, depth 7\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|tree_builder.go:BuildTree:456 | Tree construction completed: 29 nodes, 18 leaves, depth 7\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[35mDEBUG\u001b[0m|main.go:createAndBuildTree:238 | Tree building process completed successfully\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:131 | Successfully built tree with 29 nodes, 18 leaves, depth 7\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:CompleteStage:410 | Completed stage: Building Tree (elapsed: 22ms)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:StartStage:394 | Starting stage: Saving Model\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:UpdateProgress:402 | Saving Model: Serializing model to JSON: /tmp/tmpvh7h_ohe.dt (elapsed: 22ms)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:UpdateProgress:402 | Saving Model: Model saved successfully (19207 bytes) (elapsed: 22ms)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:CompleteStage:410 | Completed stage: Saving Model (elapsed: 22ms)\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:showSuccessMessage:335 | Training completed successfully!\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:showSuccessMessage:336 | Model saved to: /tmp/tmpvh7h_ohe.dt\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:showSuccessMessage:337 | Tree statistics: 29 nodes, 18 leaves, depth 7\n\u001b[36m2025-07-31 21:57:30\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:145 | Training workflow completed successfully\n", "stderr": "", "execution_time": 0.14767169952392578, "returncode": 0, "tree_structure": {"root": {"type": "decision", "feature": {"name": "income", "type": "numeric", "column_number": 1, "values": [], "numeric_range": {"min": 20077, "max": 149922}, "min": 20077, "max": 149922}, "threshold": 20268.5, "left": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 4}, "samples": 4, "confidence": 1, "impurity": 0}, "right": {"type": "decision", "feature": {"name": "customer_id", "type": "numeric", "column_number": 2, "values": [], "numeric_range": {"min": 106570, "max": 999187}, "min": 106570, "max": 999187}, "threshold": 998927.5, "left": {"type": "decision", "feature": {"name": "is_premium", "type": "numeric", "column_number": 13, "values": [], "numeric_range": {"min": 0, "max": 1}, "max": 1}, "threshold": 0.5, "left": {"type": "decision", "feature": {"name": "income", "type": "numeric", "column_number": 1, "values": [], "numeric_range": {"min": 20077, "max": 149922}, "min": 20077, "max": 149922}, "threshold": 75017.5, "left": {"type": "decision", "feature": {"name": "age", "type": "numeric", "column_number": 0, "values": [], "numeric_range": {"min": 18, "max": 79}, "min": 18, "max": 79}, "threshold": 40.5, "left": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 55}, "samples": 55, "confidence": 1, "impurity": 0}, "right": {"type": "decision", "feature": {"name": "education", "type": "categorical", "column_number": 18, "values": [], "categorical_values": ["Bachelor", "High_School", "PhD", "Master"]}, "threshold": 0, "categories": {"Bachelor": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 40}, "samples": 40, "confidence": 1, "impurity": 0}, "High_School": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 25}, "samples": 25, "confidence": 1, "impurity": 0}, "Master": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 24}, "samples": 24, "confidence": 1, "impurity": 0}, "PhD": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 23}, "samples": 23, "confidence": 1, "impurity": 0}}, "class_distribution": {"No": 65, "Yes": 47}, "samples": 112, "confidence": 0.5803571428571429, "impurity": 0.9812872088817246}, "class_distribution": {"No": 120, "Yes": 47}, "samples": 167, "confidence": 0.718562874251497, "impurity": 0.8574016128221289}, "right": {"type": "decision", "feature": {"name": "age", "type": "numeric", "column_number": 0, "values": [], "numeric_range": {"min": 18, "max": 79}, "min": 18, "max": 79}, "threshold": 40.5, "left": {"type": "decision", "feature": {"name": "education", "type": "categorical", "column_number": 18, "values": [], "categorical_values": ["Bachelor", "High_School", "PhD", "Master"]}, "threshold": 0, "categories": {"Bachelor": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 24}, "samples": 24, "confidence": 1, "impurity": 0}, "High_School": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 20}, "samples": 20, "confidence": 1, "impurity": 0}, "Master": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 15}, "samples": 15, "confidence": 1, "impurity": 0}, "PhD": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 16}, "samples": 16, "confidence": 1, "impurity": 0}}, "class_distribution": {"No": 44, "Yes": 31}, "samples": 75, "confidence": 0.5866666666666667, "impurity": 0.9782176659354248}, "right": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 131}, "samples": 131, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 44, "Yes": 162}, "samples": 206, "confidence": 0.7864077669902912, "impurity": 0.7482932859824889}, "class_distribution": {"No": 164, "Yes": 209}, "samples": 373, "confidence": 0.5603217158176944, "impurity": 0.9894752857719396}, "right": {"type": "decision", "feature": {"name": "age", "type": "numeric", "column_number": 0, "values": [], "numeric_range": {"min": 18, "max": 79}, "min": 18, "max": 79}, "threshold": 40.5, "left": {"type": "decision", "feature": {"name": "income", "type": "numeric", "column_number": 1, "values": [], "numeric_range": {"min": 20077, "max": 149922}, "min": 20077, "max": 149922}, "threshold": 72163.5, "left": {"type": "decision", "feature": {"name": "education", "type": "categorical", "column_number": 18, "values": [], "categorical_values": ["Bachelor", "High_School", "PhD", "Master"]}, "threshold": 0, "categories": {"Bachelor": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 12}, "samples": 12, "confidence": 1, "impurity": 0}, "High_School": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 16}, "samples": 16, "confidence": 1, "impurity": 0}, "Master": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 10}, "samples": 10, "confidence": 1, "impurity": 0}, "PhD": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 14}, "samples": 14, "confidence": 1, "impurity": 0}}, "class_distribution": {"No": 28, "Yes": 24}, "samples": 52, "confidence": 0.5384615384615384, "impurity": 0.9957274520849255}, "right": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 83}, "samples": 83, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 28, "Yes": 107}, "samples": 135, "confidence": 0.7925925925925926, "impurity": 0.7364977795505668}, "right": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 286}, "samples": 286, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 28, "Yes": 393}, "samples": 421, "confidence": 0.9334916864608076, "impurity": 0.3527561375432482}, "class_distribution": {"No": 192, "Yes": 602}, "samples": 794, "confidence": 0.7581863979848866, "impurity": 0.7980432506092013}, "right": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 2}, "samples": 2, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 194, "Yes": 602}, "samples": 796, "confidence": 0.7562814070351759, "impurity": 0.8011696748229913}, "class_distribution": {"No": 198, "Yes": 602}, "samples": 800, "confidence": 0.7525, "impurity": 0.8072916195433455}, "features": {"age": {"name": "age", "type": "numeric", "column_number": 0, "values": []}, "city": {"name": "city", "type": "categorical", "column_number": 10, "values": []}, "color": {"name": "color", "type": "categorical", "column_number": 8, "values": []}, "country": {"name": "country", "type": "categorical", "column_number": 11, "values": []}, "customer_id": {"name": "customer_id", "type": "numeric", "column_number": 2, "values": []}, "department": {"name": "department", "type": "categorical", "column_number": 9, "values": []}, "education": {"name": "education", "type": "categorical", "column_number": 18, "values": []}, "employment_status": {"name": "employment_status", "type": "categorical", "column_number": 17, "values": []}, "gender": {"name": "gender", "type": "categorical", "column_number": 16, "values": []}, "has_subscription": {"name": "has_subscription", "type": "categorical", "column_number": 14, "values": []}, "height_cm": {"name": "height_cm", "type": "numeric", "column_number": 3, "values": []}, "income": {"name": "income", "type": "numeric", "column_number": 1, "values": []}, "income_bracket": {"name": "income_bracket", "type": "categorical", "column_number": 21, "values": []}, "is_premium": {"name": "is_premium", "type": "numeric", "column_number": 13, "values": []}, "last_login": {"name": "last_login", "type": "categorical", "column_number": 24, "values": []}, "owns_car": {"name": "owns_car", "type": "categorical", "column_number": 15, "values": []}, "performance_grade": {"name": "performance_grade", "type": "categorical", "column_number": 22, "values": []}, "preferred_time": {"name": "preferred_time", "type": "categorical", "column_number": 25, "values": []}, "price_dollars": {"name": "price_dollars", "type": "numeric", "column_number": 7, "values": []}, "satisfaction_rating": {"name": "satisfaction_rating", "type": "numeric", "column_number": 19, "values": []}, "score_0_1": {"name": "score_0_1", "type": "numeric", "column_number": 5, "values": []}, "score_0_100": {"name": "score_0_100", "type": "numeric", "column_number": 6, "values": []}, "shirt_size": {"name": "shirt_size", "type": "categorical", "column_number": 20, "values": []}, "signup_date": {"name": "signup_date", "type": "categorical", "column_number": 23, "values": []}, "weight_kg": {"name": "weight_kg", "type": "numeric", "column_number": 4, "values": []}, "zip_code": {"name": "zip_code", "type": "numeric", "column_number": 12, "values": []}}, "features_by_index": [{"name": "age", "type": "numeric", "column_number": 0, "values": []}, {"name": "income", "type": "numeric", "column_number": 1, "values": []}, {"name": "customer_id", "type": "numeric", "column_number": 2, "values": []}, {"name": "height_cm", "type": "numeric", "column_number": 3, "values": []}, {"name": "weight_kg", "type": "numeric", "column_number": 4, "values": []}, {"name": "score_0_1", "type": "numeric", "column_number": 5, "values": []}, {"name": "score_0_100", "type": "numeric", "column_number": 6, "values": []}, {"name": "price_dollars", "type": "numeric", "column_number": 7, "values": []}, {"name": "color", "type": "categorical", "column_number": 8, "values": []}, {"name": "department", "type": "categorical", "column_number": 9, "values": []}, {"name": "city", "type": "categorical", "column_number": 10, "values": []}, {"name": "country", "type": "categorical", "column_number": 11, "values": []}, {"name": "zip_code", "type": "numeric", "column_number": 12, "values": []}, {"name": "is_premium", "type": "numeric", "column_number": 13, "values": []}, {"name": "has_subscription", "type": "categorical", "column_number": 14, "values": []}, {"name": "owns_car", "type": "categorical", "column_number": 15, "values": []}, {"name": "gender", "type": "categorical", "column_number": 16, "values": []}, {"name": "employment_status", "type": "categorical", "column_number": 17, "values": []}, {"name": "education", "type": "categorical", "column_number": 18, "values": []}, {"name": "satisfaction_rating", "type": "numeric", "column_number": 19, "values": []}, {"name": "shirt_size", "type": "categorical", "column_number": 20, "values": []}, {"name": "income_bracket", "type": "categorical", "column_number": 21, "values": []}, {"name": "performance_grade", "type": "categorical", "column_number": 22, "values": []}, {"name": "signup_date", "type": "categorical", "column_number": 23, "values": []}, {"name": "last_login", "type": "categorical", "column_number": 24, "values": []}, {"name": "preferred_time", "type": "categorical", "column_number": 25, "values": []}], "target_type": "categorical", "target_column": "y", "config": {"max_depth": 10, "min_samples": 2, "target_type": "categorical", "criterion": "entropy", "max_features": -1}, "node_count": 29, "leaf_count": 18, "depth": 7}}, "comparison": {"schema_differences": [], "field_naming": [], "data_types": [], "structure_analysis": {}, "go_fields": ["root", "type", "feature", "name", "column_number", "values", "numeric_range", "min", "max", "threshold", "left", "prediction", "class_distribution", "No", "samples", "confidence", "impurity", "right", "categorical_values", "categories", "Bachelor", "High_School", "Master", "Yes", "PhD", "features", "age", "city", "color", "country", "customer_id", "department", "education", "employment_status", "gender", "has_subscription", "height_cm", "income", "income_bracket", "is_premium", "last_login", "owns_car", "performance_grade", "preferred_time", "price_dollars", "satisfaction_rating", "score_0_1", "score_0_100", "shirt_size", "signup_date", "weight_kg", "zip_code", "features_by_index", "target_type", "target_column", "config", "max_depth", "min_samples", "criterion", "max_features", "node_count", "leaf_count", "depth"], "go_data_types": {"root.root": "dict", "root.root.type": "str", "root.root.feature": "dict", "root.root.feature.name": "str", "root.root.feature.type": "str", "root.root.feature.column_number": "int", "root.root.feature.values": "list", "root.root.feature.numeric_range": "dict", "root.root.feature.numeric_range.min": "int", "root.root.feature.numeric_range.max": "int", "root.root.feature.min": "int", "root.root.feature.max": "int", "root.root.threshold": "float", "root.root.left": "dict", "root.root.left.type": "str", "root.root.left.threshold": "int", "root.root.left.prediction": "str", "root.root.left.class_distribution": "dict", "root.root.left.class_distribution.No": "int", "root.root.left.samples": "int", "root.root.left.confidence": "int", "root.root.left.impurity": "int", "root.root.right": "dict", "root.root.right.type": "str", "root.root.right.feature": "dict", "root.root.right.feature.name": "str", "root.root.right.feature.type": "str", "root.root.right.feature.column_number": "int", "root.root.right.feature.values": "list", "root.root.right.feature.numeric_range": "dict", "root.root.right.feature.numeric_range.min": "int", "root.root.right.feature.numeric_range.max": "int", "root.root.right.feature.min": "int", "root.root.right.feature.max": "int", "root.root.right.threshold": "float", "root.root.right.left": "dict", "root.root.right.left.type": "str", "root.root.right.left.feature": "dict", "root.root.right.left.feature.name": "str", "root.root.right.left.feature.type": "str", "root.root.right.left.feature.column_number": "int", "root.root.right.left.feature.values": "list", "root.root.right.left.feature.numeric_range": "dict", "root.root.right.left.feature.numeric_range.min": "int", "root.root.right.left.feature.numeric_range.max": "int", "root.root.right.left.feature.max": "int", "root.root.right.left.threshold": "float", "root.root.right.left.left": "dict", "root.root.right.left.left.type": "str", "root.root.right.left.left.feature": "dict", "root.root.right.left.left.feature.name": "str", "root.root.right.left.left.feature.type": "str", "root.root.right.left.left.feature.column_number": "int", "root.root.right.left.left.feature.values": "list", "root.root.right.left.left.feature.numeric_range": "dict", "root.root.right.left.left.feature.numeric_range.min": "int", "root.root.right.left.left.feature.numeric_range.max": "int", "root.root.right.left.left.feature.min": "int", "root.root.right.left.left.feature.max": "int", "root.root.right.left.left.threshold": "float", "root.root.right.left.left.left": "dict", "root.root.right.left.left.left.type": "str", "root.root.right.left.left.left.feature": "dict", "root.root.right.left.left.left.feature.name": "str", "root.root.right.left.left.left.feature.type": "str", "root.root.right.left.left.left.feature.column_number": "int", "root.root.right.left.left.left.feature.values": "list", "root.root.right.left.left.left.feature.numeric_range": "dict", "root.root.right.left.left.left.feature.numeric_range.min": "int", "root.root.right.left.left.left.feature.numeric_range.max": "int", "root.root.right.left.left.left.feature.min": "int", "root.root.right.left.left.left.feature.max": "int", "root.root.right.left.left.left.threshold": "float", "root.root.right.left.left.left.left": "dict", "root.root.right.left.left.left.left.type": "str", "root.root.right.left.left.left.left.threshold": "int", "root.root.right.left.left.left.left.prediction": "str", "root.root.right.left.left.left.left.class_distribution": "dict", "root.root.right.left.left.left.left.class_distribution.No": "int", "root.root.right.left.left.left.left.samples": "int", "root.root.right.left.left.left.left.confidence": "int", "root.root.right.left.left.left.left.impurity": "int", "root.root.right.left.left.left.right": "dict", "root.root.right.left.left.left.right.type": "str", "root.root.right.left.left.left.right.feature": "dict", "root.root.right.left.left.left.right.feature.name": "str", "root.root.right.left.left.left.right.feature.type": "str", "root.root.right.left.left.left.right.feature.column_number": "int", "root.root.right.left.left.left.right.feature.values": "list", "root.root.right.left.left.left.right.feature.categorical_values": "list", "root.root.right.left.left.left.right.feature.categorical_values_list_type": "str", "root.root.right.left.left.left.right.threshold": "int", "root.root.right.left.left.left.right.categories": "dict", "root.root.right.left.left.left.right.categories.Bachelor": "dict", "root.root.right.left.left.left.right.categories.Bachelor.type": "str", "root.root.right.left.left.left.right.categories.Bachelor.threshold": "int", "root.root.right.left.left.left.right.categories.Bachelor.prediction": "str", "root.root.right.left.left.left.right.categories.Bachelor.class_distribution": "dict", "root.root.right.left.left.left.right.categories.Bachelor.class_distribution.No": "int", "root.root.right.left.left.left.right.categories.Bachelor.samples": "int", "root.root.right.left.left.left.right.categories.Bachelor.confidence": "int", "root.root.right.left.left.left.right.categories.Bachelor.impurity": "int", "root.root.right.left.left.left.right.categories.High_School": "dict", "root.root.right.left.left.left.right.categories.High_School.type": "str", "root.root.right.left.left.left.right.categories.High_School.threshold": "int", "root.root.right.left.left.left.right.categories.High_School.prediction": "str", "root.root.right.left.left.left.right.categories.High_School.class_distribution": "dict", "root.root.right.left.left.left.right.categories.High_School.class_distribution.No": "int", "root.root.right.left.left.left.right.categories.High_School.samples": "int", "root.root.right.left.left.left.right.categories.High_School.confidence": "int", "root.root.right.left.left.left.right.categories.High_School.impurity": "int", "root.root.right.left.left.left.right.categories.Master": "dict", "root.root.right.left.left.left.right.categories.Master.type": "str", "root.root.right.left.left.left.right.categories.Master.threshold": "int", "root.root.right.left.left.left.right.categories.Master.prediction": "str", "root.root.right.left.left.left.right.categories.Master.class_distribution": "dict", "root.root.right.left.left.left.right.categories.Master.class_distribution.Yes": "int", "root.root.right.left.left.left.right.categories.Master.samples": "int", "root.root.right.left.left.left.right.categories.Master.confidence": "int", "root.root.right.left.left.left.right.categories.Master.impurity": "int", "root.root.right.left.left.left.right.categories.PhD": "dict", "root.root.right.left.left.left.right.categories.PhD.type": "str", "root.root.right.left.left.left.right.categories.PhD.threshold": "int", "root.root.right.left.left.left.right.categories.PhD.prediction": "str", "root.root.right.left.left.left.right.categories.PhD.class_distribution": "dict", "root.root.right.left.left.left.right.categories.PhD.class_distribution.Yes": "int", "root.root.right.left.left.left.right.categories.PhD.samples": "int", "root.root.right.left.left.left.right.categories.PhD.confidence": "int", "root.root.right.left.left.left.right.categories.PhD.impurity": "int", "root.root.right.left.left.left.right.class_distribution": "dict", "root.root.right.left.left.left.right.class_distribution.No": "int", "root.root.right.left.left.left.right.class_distribution.Yes": "int", "root.root.right.left.left.left.right.samples": "int", "root.root.right.left.left.left.right.confidence": "float", "root.root.right.left.left.left.right.impurity": "float", "root.root.right.left.left.left.class_distribution": "dict", "root.root.right.left.left.left.class_distribution.No": "int", "root.root.right.left.left.left.class_distribution.Yes": "int", "root.root.right.left.left.left.samples": "int", "root.root.right.left.left.left.confidence": "float", "root.root.right.left.left.left.impurity": "float", "root.root.right.left.left.right": "dict", "root.root.right.left.left.right.type": "str", "root.root.right.left.left.right.feature": "dict", "root.root.right.left.left.right.feature.name": "str", "root.root.right.left.left.right.feature.type": "str", "root.root.right.left.left.right.feature.column_number": "int", "root.root.right.left.left.right.feature.values": "list", "root.root.right.left.left.right.feature.numeric_range": "dict", "root.root.right.left.left.right.feature.numeric_range.min": "int", "root.root.right.left.left.right.feature.numeric_range.max": "int", "root.root.right.left.left.right.feature.min": "int", "root.root.right.left.left.right.feature.max": "int", "root.root.right.left.left.right.threshold": "float", "root.root.right.left.left.right.left": "dict", "root.root.right.left.left.right.left.type": "str", "root.root.right.left.left.right.left.feature": "dict", "root.root.right.left.left.right.left.feature.name": "str", "root.root.right.left.left.right.left.feature.type": "str", "root.root.right.left.left.right.left.feature.column_number": "int", "root.root.right.left.left.right.left.feature.values": "list", "root.root.right.left.left.right.left.feature.categorical_values": "list", "root.root.right.left.left.right.left.feature.categorical_values_list_type": "str", "root.root.right.left.left.right.left.threshold": "int", "root.root.right.left.left.right.left.categories": "dict", "root.root.right.left.left.right.left.categories.Bachelor": "dict", "root.root.right.left.left.right.left.categories.Bachelor.type": "str", "root.root.right.left.left.right.left.categories.Bachelor.threshold": "int", "root.root.right.left.left.right.left.categories.Bachelor.prediction": "str", "root.root.right.left.left.right.left.categories.Bachelor.class_distribution": "dict", "root.root.right.left.left.right.left.categories.Bachelor.class_distribution.No": "int", "root.root.right.left.left.right.left.categories.Bachelor.samples": "int", "root.root.right.left.left.right.left.categories.Bachelor.confidence": "int", "root.root.right.left.left.right.left.categories.Bachelor.impurity": "int", "root.root.right.left.left.right.left.categories.High_School": "dict", "root.root.right.left.left.right.left.categories.High_School.type": "str", "root.root.right.left.left.right.left.categories.High_School.threshold": "int", "root.root.right.left.left.right.left.categories.High_School.prediction": "str", "root.root.right.left.left.right.left.categories.High_School.class_distribution": "dict", "root.root.right.left.left.right.left.categories.High_School.class_distribution.No": "int", "root.root.right.left.left.right.left.categories.High_School.samples": "int", "root.root.right.left.left.right.left.categories.High_School.confidence": "int", "root.root.right.left.left.right.left.categories.High_School.impurity": "int", "root.root.right.left.left.right.left.categories.Master": "dict", "root.root.right.left.left.right.left.categories.Master.type": "str", "root.root.right.left.left.right.left.categories.Master.threshold": "int", "root.root.right.left.left.right.left.categories.Master.prediction": "str", "root.root.right.left.left.right.left.categories.Master.class_distribution": "dict", "root.root.right.left.left.right.left.categories.Master.class_distribution.Yes": "int", "root.root.right.left.left.right.left.categories.Master.samples": "int", "root.root.right.left.left.right.left.categories.Master.confidence": "int", "root.root.right.left.left.right.left.categories.Master.impurity": "int", "root.root.right.left.left.right.left.categories.PhD": "dict", "root.root.right.left.left.right.left.categories.PhD.type": "str", "root.root.right.left.left.right.left.categories.PhD.threshold": "int", "root.root.right.left.left.right.left.categories.PhD.prediction": "str", "root.root.right.left.left.right.left.categories.PhD.class_distribution": "dict", "root.root.right.left.left.right.left.categories.PhD.class_distribution.Yes": "int", "root.root.right.left.left.right.left.categories.PhD.samples": "int", "root.root.right.left.left.right.left.categories.PhD.confidence": "int", "root.root.right.left.left.right.left.categories.PhD.impurity": "int", "root.root.right.left.left.right.left.class_distribution": "dict", "root.root.right.left.left.right.left.class_distribution.No": "int", "root.root.right.left.left.right.left.class_distribution.Yes": "int", "root.root.right.left.left.right.left.samples": "int", "root.root.right.left.left.right.left.confidence": "float", "root.root.right.left.left.right.left.impurity": "float", "root.root.right.left.left.right.right": "dict", "root.root.right.left.left.right.right.type": "str", "root.root.right.left.left.right.right.threshold": "int", "root.root.right.left.left.right.right.prediction": "str", "root.root.right.left.left.right.right.class_distribution": "dict", "root.root.right.left.left.right.right.class_distribution.Yes": "int", "root.root.right.left.left.right.right.samples": "int", "root.root.right.left.left.right.right.confidence": "int", "root.root.right.left.left.right.right.impurity": "int", "root.root.right.left.left.right.class_distribution": "dict", "root.root.right.left.left.right.class_distribution.No": "int", "root.root.right.left.left.right.class_distribution.Yes": "int", "root.root.right.left.left.right.samples": "int", "root.root.right.left.left.right.confidence": "float", "root.root.right.left.left.right.impurity": "float", "root.root.right.left.left.class_distribution": "dict", "root.root.right.left.left.class_distribution.No": "int", "root.root.right.left.left.class_distribution.Yes": "int", "root.root.right.left.left.samples": "int", "root.root.right.left.left.confidence": "float", "root.root.right.left.left.impurity": "float", "root.root.right.left.right": "dict", "root.root.right.left.right.type": "str", "root.root.right.left.right.feature": "dict", "root.root.right.left.right.feature.name": "str", "root.root.right.left.right.feature.type": "str", "root.root.right.left.right.feature.column_number": "int", "root.root.right.left.right.feature.values": "list", "root.root.right.left.right.feature.numeric_range": "dict", "root.root.right.left.right.feature.numeric_range.min": "int", "root.root.right.left.right.feature.numeric_range.max": "int", "root.root.right.left.right.feature.min": "int", "root.root.right.left.right.feature.max": "int", "root.root.right.left.right.threshold": "float", "root.root.right.left.right.left": "dict", "root.root.right.left.right.left.type": "str", "root.root.right.left.right.left.feature": "dict", "root.root.right.left.right.left.feature.name": "str", "root.root.right.left.right.left.feature.type": "str", "root.root.right.left.right.left.feature.column_number": "int", "root.root.right.left.right.left.feature.values": "list", "root.root.right.left.right.left.feature.numeric_range": "dict", "root.root.right.left.right.left.feature.numeric_range.min": "int", "root.root.right.left.right.left.feature.numeric_range.max": "int", "root.root.right.left.right.left.feature.min": "int", "root.root.right.left.right.left.feature.max": "int", "root.root.right.left.right.left.threshold": "float", "root.root.right.left.right.left.left": "dict", "root.root.right.left.right.left.left.type": "str", "root.root.right.left.right.left.left.feature": "dict", "root.root.right.left.right.left.left.feature.name": "str", "root.root.right.left.right.left.left.feature.type": "str", "root.root.right.left.right.left.left.feature.column_number": "int", "root.root.right.left.right.left.left.feature.values": "list", "root.root.right.left.right.left.left.feature.categorical_values": "list", "root.root.right.left.right.left.left.feature.categorical_values_list_type": "str", "root.root.right.left.right.left.left.threshold": "int", "root.root.right.left.right.left.left.categories": "dict", "root.root.right.left.right.left.left.categories.Bachelor": "dict", "root.root.right.left.right.left.left.categories.Bachelor.type": "str", "root.root.right.left.right.left.left.categories.Bachelor.threshold": "int", "root.root.right.left.right.left.left.categories.Bachelor.prediction": "str", "root.root.right.left.right.left.left.categories.Bachelor.class_distribution": "dict", "root.root.right.left.right.left.left.categories.Bachelor.class_distribution.No": "int", "root.root.right.left.right.left.left.categories.Bachelor.samples": "int", "root.root.right.left.right.left.left.categories.Bachelor.confidence": "int", "root.root.right.left.right.left.left.categories.Bachelor.impurity": "int", "root.root.right.left.right.left.left.categories.High_School": "dict", "root.root.right.left.right.left.left.categories.High_School.type": "str", "root.root.right.left.right.left.left.categories.High_School.threshold": "int", "root.root.right.left.right.left.left.categories.High_School.prediction": "str", "root.root.right.left.right.left.left.categories.High_School.class_distribution": "dict", "root.root.right.left.right.left.left.categories.High_School.class_distribution.No": "int", "root.root.right.left.right.left.left.categories.High_School.samples": "int", "root.root.right.left.right.left.left.categories.High_School.confidence": "int", "root.root.right.left.right.left.left.categories.High_School.impurity": "int", "root.root.right.left.right.left.left.categories.Master": "dict", "root.root.right.left.right.left.left.categories.Master.type": "str", "root.root.right.left.right.left.left.categories.Master.threshold": "int", "root.root.right.left.right.left.left.categories.Master.prediction": "str", "root.root.right.left.right.left.left.categories.Master.class_distribution": "dict", "root.root.right.left.right.left.left.categories.Master.class_distribution.Yes": "int", "root.root.right.left.right.left.left.categories.Master.samples": "int", "root.root.right.left.right.left.left.categories.Master.confidence": "int", "root.root.right.left.right.left.left.categories.Master.impurity": "int", "root.root.right.left.right.left.left.categories.PhD": "dict", "root.root.right.left.right.left.left.categories.PhD.type": "str", "root.root.right.left.right.left.left.categories.PhD.threshold": "int", "root.root.right.left.right.left.left.categories.PhD.prediction": "str", "root.root.right.left.right.left.left.categories.PhD.class_distribution": "dict", "root.root.right.left.right.left.left.categories.PhD.class_distribution.Yes": "int", "root.root.right.left.right.left.left.categories.PhD.samples": "int", "root.root.right.left.right.left.left.categories.PhD.confidence": "int", "root.root.right.left.right.left.left.categories.PhD.impurity": "int", "root.root.right.left.right.left.left.class_distribution": "dict", "root.root.right.left.right.left.left.class_distribution.No": "int", "root.root.right.left.right.left.left.class_distribution.Yes": "int", "root.root.right.left.right.left.left.samples": "int", "root.root.right.left.right.left.left.confidence": "float", "root.root.right.left.right.left.left.impurity": "float", "root.root.right.left.right.left.right": "dict", "root.root.right.left.right.left.right.type": "str", "root.root.right.left.right.left.right.threshold": "int", "root.root.right.left.right.left.right.prediction": "str", "root.root.right.left.right.left.right.class_distribution": "dict", "root.root.right.left.right.left.right.class_distribution.Yes": "int", "root.root.right.left.right.left.right.samples": "int", "root.root.right.left.right.left.right.confidence": "int", "root.root.right.left.right.left.right.impurity": "int", "root.root.right.left.right.left.class_distribution": "dict", "root.root.right.left.right.left.class_distribution.No": "int", "root.root.right.left.right.left.class_distribution.Yes": "int", "root.root.right.left.right.left.samples": "int", "root.root.right.left.right.left.confidence": "float", "root.root.right.left.right.left.impurity": "float", "root.root.right.left.right.right": "dict", "root.root.right.left.right.right.type": "str", "root.root.right.left.right.right.threshold": "int", "root.root.right.left.right.right.prediction": "str", "root.root.right.left.right.right.class_distribution": "dict", "root.root.right.left.right.right.class_distribution.Yes": "int", "root.root.right.left.right.right.samples": "int", "root.root.right.left.right.right.confidence": "int", "root.root.right.left.right.right.impurity": "int", "root.root.right.left.right.class_distribution": "dict", "root.root.right.left.right.class_distribution.No": "int", "root.root.right.left.right.class_distribution.Yes": "int", "root.root.right.left.right.samples": "int", "root.root.right.left.right.confidence": "float", "root.root.right.left.right.impurity": "float", "root.root.right.left.class_distribution": "dict", "root.root.right.left.class_distribution.No": "int", "root.root.right.left.class_distribution.Yes": "int", "root.root.right.left.samples": "int", "root.root.right.left.confidence": "float", "root.root.right.left.impurity": "float", "root.root.right.right": "dict", "root.root.right.right.type": "str", "root.root.right.right.threshold": "int", "root.root.right.right.prediction": "str", "root.root.right.right.class_distribution": "dict", "root.root.right.right.class_distribution.No": "int", "root.root.right.right.samples": "int", "root.root.right.right.confidence": "int", "root.root.right.right.impurity": "int", "root.root.right.class_distribution": "dict", "root.root.right.class_distribution.No": "int", "root.root.right.class_distribution.Yes": "int", "root.root.right.samples": "int", "root.root.right.confidence": "float", "root.root.right.impurity": "float", "root.root.class_distribution": "dict", "root.root.class_distribution.No": "int", "root.root.class_distribution.Yes": "int", "root.root.samples": "int", "root.root.confidence": "float", "root.root.impurity": "float", "root.features": "dict", "root.features.age": "dict", "root.features.age.name": "str", "root.features.age.type": "str", "root.features.age.column_number": "int", "root.features.age.values": "list", "root.features.city": "dict", "root.features.city.name": "str", "root.features.city.type": "str", "root.features.city.column_number": "int", "root.features.city.values": "list", "root.features.color": "dict", "root.features.color.name": "str", "root.features.color.type": "str", "root.features.color.column_number": "int", "root.features.color.values": "list", "root.features.country": "dict", "root.features.country.name": "str", "root.features.country.type": "str", "root.features.country.column_number": "int", "root.features.country.values": "list", "root.features.customer_id": "dict", "root.features.customer_id.name": "str", "root.features.customer_id.type": "str", "root.features.customer_id.column_number": "int", "root.features.customer_id.values": "list", "root.features.department": "dict", "root.features.department.name": "str", "root.features.department.type": "str", "root.features.department.column_number": "int", "root.features.department.values": "list", "root.features.education": "dict", "root.features.education.name": "str", "root.features.education.type": "str", "root.features.education.column_number": "int", "root.features.education.values": "list", "root.features.employment_status": "dict", "root.features.employment_status.name": "str", "root.features.employment_status.type": "str", "root.features.employment_status.column_number": "int", "root.features.employment_status.values": "list", "root.features.gender": "dict", "root.features.gender.name": "str", "root.features.gender.type": "str", "root.features.gender.column_number": "int", "root.features.gender.values": "list", "root.features.has_subscription": "dict", "root.features.has_subscription.name": "str", "root.features.has_subscription.type": "str", "root.features.has_subscription.column_number": "int", "root.features.has_subscription.values": "list", "root.features.height_cm": "dict", "root.features.height_cm.name": "str", "root.features.height_cm.type": "str", "root.features.height_cm.column_number": "int", "root.features.height_cm.values": "list", "root.features.income": "dict", "root.features.income.name": "str", "root.features.income.type": "str", "root.features.income.column_number": "int", "root.features.income.values": "list", "root.features.income_bracket": "dict", "root.features.income_bracket.name": "str", "root.features.income_bracket.type": "str", "root.features.income_bracket.column_number": "int", "root.features.income_bracket.values": "list", "root.features.is_premium": "dict", "root.features.is_premium.name": "str", "root.features.is_premium.type": "str", "root.features.is_premium.column_number": "int", "root.features.is_premium.values": "list", "root.features.last_login": "dict", "root.features.last_login.name": "str", "root.features.last_login.type": "str", "root.features.last_login.column_number": "int", "root.features.last_login.values": "list", "root.features.owns_car": "dict", "root.features.owns_car.name": "str", "root.features.owns_car.type": "str", "root.features.owns_car.column_number": "int", "root.features.owns_car.values": "list", "root.features.performance_grade": "dict", "root.features.performance_grade.name": "str", "root.features.performance_grade.type": "str", "root.features.performance_grade.column_number": "int", "root.features.performance_grade.values": "list", "root.features.preferred_time": "dict", "root.features.preferred_time.name": "str", "root.features.preferred_time.type": "str", "root.features.preferred_time.column_number": "int", "root.features.preferred_time.values": "list", "root.features.price_dollars": "dict", "root.features.price_dollars.name": "str", "root.features.price_dollars.type": "str", "root.features.price_dollars.column_number": "int", "root.features.price_dollars.values": "list", "root.features.satisfaction_rating": "dict", "root.features.satisfaction_rating.name": "str", "root.features.satisfaction_rating.type": "str", "root.features.satisfaction_rating.column_number": "int", "root.features.satisfaction_rating.values": "list", "root.features.score_0_1": "dict", "root.features.score_0_1.name": "str", "root.features.score_0_1.type": "str", "root.features.score_0_1.column_number": "int", "root.features.score_0_1.values": "list", "root.features.score_0_100": "dict", "root.features.score_0_100.name": "str", "root.features.score_0_100.type": "str", "root.features.score_0_100.column_number": "int", "root.features.score_0_100.values": "list", "root.features.shirt_size": "dict", "root.features.shirt_size.name": "str", "root.features.shirt_size.type": "str", "root.features.shirt_size.column_number": "int", "root.features.shirt_size.values": "list", "root.features.signup_date": "dict", "root.features.signup_date.name": "str", "root.features.signup_date.type": "str", "root.features.signup_date.column_number": "int", "root.features.signup_date.values": "list", "root.features.weight_kg": "dict", "root.features.weight_kg.name": "str", "root.features.weight_kg.type": "str", "root.features.weight_kg.column_number": "int", "root.features.weight_kg.values": "list", "root.features.zip_code": "dict", "root.features.zip_code.name": "str", "root.features.zip_code.type": "str", "root.features.zip_code.column_number": "int", "root.features.zip_code.values": "list", "root.features_by_index": "list", "root.features_by_index_list_type": "dict", "root.features_by_index[0].name": "str", "root.features_by_index[0].type": "str", "root.features_by_index[0].column_number": "int", "root.features_by_index[0].values": "list", "root.target_type": "str", "root.target_column": "str", "root.config": "dict", "root.config.max_depth": "int", "root.config.min_samples": "int", "root.config.target_type": "str", "root.config.criterion": "str", "root.config.max_features": "int", "root.node_count": "int", "root.leaf_count": "int", "root.depth": "int"}}, "performance": {"execution_times": {"python": 0.5467548370361328, "go": 0.14767169952392578}, "memory_analysis": {}, "output_sizes": {"go_json_size": 11117, "go_node_count": 1}, "speed_ratio": 3.7025025025025027, "faster_implementation": "Go"}}