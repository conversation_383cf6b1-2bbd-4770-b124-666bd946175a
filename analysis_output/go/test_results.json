{"training": {"stdout": "\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:main:39 | Mulberri application starting\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:80 | Starting training workflow with input: testdata/testdata_train.csv, target: y, output: /tmp/tmp97ly0n6b.dt\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:StartStage:394 | Starting stage: Loading Data\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|csv_loader.go:ReadTrainingCSV:159 | Starting CSV read operation: file=testdata/testdata_train.csv, target=y\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|csv_loader.go:ReadTrainingCSV:179 | Opening CSV file: testdata/testdata_train.csv\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|csv_loader.go:ReadTrainingCSV:202 | File size: 157258 bytes\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|csv_loader.go:ReadTrainingCSV:220 | Reading CSV headers\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|csv_loader.go:ReadTrainingCSV:241 | Found 27 headers: [age income customer_id height_cm weight_kg score_0_1 score_0_100 price_dollars color department city country zip_code is_premium has_subscription owns_car gender employment_status education satisfaction_rating shirt_size income_bracket performance_grade signup_date last_login preferred_time y]\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|csv_loader.go:ReadTrainingCSV:256 | Looking for target column: y\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|csv_loader.go:ReadTrainingCSV:274 | Target column 'y' found at index 26\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|csv_loader.go:ReadTrainingCSV:283 | Starting to read data rows\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|csv_loader.go:ReadTrainingCSV:317 | Successfully read 800 data rows\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|csv_loader.go:ReadTrainingCSV:329 | Successfully loaded CSV: 800 rows, 27 columns, target='y'\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|main.go:handleTrainCommand:91 | Loaded 800 rows with 27 columns\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:CompleteStage:410 | Completed stage: Loading Data (elapsed: 4ms)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:StartStage:394 | Starting stage: Processing Features\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|csv_loader.go:ExtractRecordsAndTarget:346 | Extracting features and targets: 800 rows, target='y' at index 26\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|csv_loader.go:ExtractRecordsAndTarget:375 | Created 26 feature headers: [age income customer_id height_cm weight_kg score_0_1 score_0_100 price_dollars color department city country zip_code is_premium has_subscription owns_car gender employment_status education satisfaction_rating shirt_size income_bracket performance_grade signup_date last_login preferred_time]\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|csv_loader.go:ExtractRecordsAndTarget:404 | Successfully extracted 800 records with 26 features each\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:createFeatureObjects:300 | No feature info file provided, using automatic feature detection\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:238 | Creating features: 26 headers, 800 rows\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'age' at index 0\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'age' (type: numeric)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'income' at index 1\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'income' (type: numeric)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'customer_id' at index 2\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'customer_id' (type: numeric)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'height_cm' at index 3\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'height_cm' (type: numeric)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'weight_kg' at index 4\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'weight_kg' (type: numeric)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'score_0_1' at index 5\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'score_0_1' (type: numeric)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'score_0_100' at index 6\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'score_0_100' (type: numeric)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'price_dollars' at index 7\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'price_dollars' (type: numeric)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'color' at index 8\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'color' (type: categorical)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'department' at index 9\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'department' (type: categorical)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'city' at index 10\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'city' (type: categorical)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'country' at index 11\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'country' (type: categorical)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'zip_code' at index 12\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'zip_code' (type: numeric)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'is_premium' at index 13\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'is_premium' (type: numeric)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'has_subscription' at index 14\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'has_subscription' (type: categorical)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'owns_car' at index 15\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'owns_car' (type: categorical)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'gender' at index 16\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'gender' (type: categorical)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'employment_status' at index 17\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'employment_status' (type: categorical)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'education' at index 18\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'education' (type: categorical)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'satisfaction_rating' at index 19\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'satisfaction_rating' (type: numeric)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'shirt_size' at index 20\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'shirt_size' (type: categorical)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'income_bracket' at index 21\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'income_bracket' (type: categorical)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'performance_grade' at index 22\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'performance_grade' (type: categorical)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'signup_date' at index 23\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'signup_date' (type: categorical)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'last_login' at index 24\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'last_login' (type: categorical)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:270 | Creating feature 'preferred_time' at index 25\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|feature_creation.go:CreateFeaturesWithConfig:283 | Successfully created feature 'preferred_time' (type: categorical)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|feature_creation.go:CreateFeaturesWithConfig:287 | Successfully created 26 features\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|main.go:handleTrainCommand:109 | Created 26 features from 800 samples\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature age: type=numeric, column=0\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature income: type=numeric, column=1\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature customer_id: type=numeric, column=2\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature height_cm: type=numeric, column=3\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature weight_kg: type=numeric, column=4\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature score_0_1: type=numeric, column=5\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature score_0_100: type=numeric, column=6\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature price_dollars: type=numeric, column=7\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature color: type=categorical, column=8\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature department: type=categorical, column=9\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature city: type=categorical, column=10\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature country: type=categorical, column=11\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature zip_code: type=numeric, column=12\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature is_premium: type=numeric, column=13\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature has_subscription: type=categorical, column=14\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature owns_car: type=categorical, column=15\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature gender: type=categorical, column=16\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature employment_status: type=categorical, column=17\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature education: type=categorical, column=18\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature satisfaction_rating: type=numeric, column=19\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature shirt_size: type=categorical, column=20\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature income_bracket: type=categorical, column=21\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature performance_grade: type=categorical, column=22\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature signup_date: type=categorical, column=23\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature last_login: type=categorical, column=24\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:113 | Feature preferred_time: type=categorical, column=25\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:UpdateProgress:402 | Processing Features: Created 26 features (elapsed: 24ms)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:CompleteStage:410 | Completed stage: Processing Features (elapsed: 24ms)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:StartStage:394 | Starting stage: Building Tree\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|dataset.go:NewCSVDatasetSmart:203 | No datetime features found, using basic CSV dataset\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|main.go:handleTrainCommand:124 | Created dataset with 800 samples\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|main.go:createAndBuildTree:195 | Creating splitter with criterion: entropy, min_samples_split: 2, min_samples_leaf: 1\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|main.go:createAndBuildTree:212 | Creating tree builder with max_depth: 10\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|main.go:createAndBuildTree:227 | Starting tree building process\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|tree_builder.go:BuildTree:429 | Starting tree construction with 800 samples and 26 features\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | agenumeric0\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='age', type=numeric\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'age' at index 0\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | incomenumeric1\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='income', type=numeric\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'income' at index 1\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | customer_idnumeric2\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='customer_id', type=numeric\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'customer_id' at index 2\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | height_cmnumeric3\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='height_cm', type=numeric\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'height_cm' at index 3\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | weight_kgnumeric4\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='weight_kg', type=numeric\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'weight_kg' at index 4\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | score_0_1numeric5\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='score_0_1', type=numeric\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'score_0_1' at index 5\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | score_0_100numeric6\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='score_0_100', type=numeric\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'score_0_100' at index 6\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | price_dollarsnumeric7\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='price_dollars', type=numeric\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'price_dollars' at index 7\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | colorcategorical8\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='color', type=categorical\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'color' at index 8\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | departmentcategorical9\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='department', type=categorical\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'department' at index 9\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | citycategorical10\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='city', type=categorical\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'city' at index 10\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | countrycategorical11\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='country', type=categorical\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'country' at index 11\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | zip_codenumeric12\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='zip_code', type=numeric\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'zip_code' at index 12\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | is_premiumnumeric13\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='is_premium', type=numeric\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'is_premium' at index 13\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | has_subscriptioncategorical14\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='has_subscription', type=categorical\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'has_subscription' at index 14\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | owns_carcategorical15\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='owns_car', type=categorical\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'owns_car' at index 15\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | gendercategorical16\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='gender', type=categorical\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'gender' at index 16\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | employment_statuscategorical17\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='employment_status', type=categorical\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'employment_status' at index 17\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | educationcategorical18\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='education', type=categorical\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'education' at index 18\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | satisfaction_ratingnumeric19\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='satisfaction_rating', type=numeric\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'satisfaction_rating' at index 19\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | shirt_sizecategorical20\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='shirt_size', type=categorical\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'shirt_size' at index 20\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | income_bracketcategorical21\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='income_bracket', type=categorical\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'income_bracket' at index 21\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | performance_gradecategorical22\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='performance_grade', type=categorical\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'performance_grade' at index 22\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | signup_datecategorical23\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='signup_date', type=categorical\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'signup_date' at index 23\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | last_logincategorical24\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='last_login', type=categorical\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'last_login' at index 24\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:createDecisionTreeContainer:604 | preferred_timecategorical25\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:400 | Adding feature: name='preferred_time', type=categorical\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:AddFeature:440 | Successfully added feature 'preferred_time' at index 25\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:buildNode:630 | Building node root at depth 0 with 800 samples\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:84 | Starting split evaluation: 800 samples, 26 features\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:117 | Base impurity calculated: 0.807292\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:135 | Best split found: feature=income, gain_ratio=0.223007\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:buildNode:630 | Building node root_L at depth 1 with 4 samples\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:buildNode:630 | Building node root_R at depth 1 with 796 samples\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:84 | Starting split evaluation: 796 samples, 26 features\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:117 | Base impurity calculated: 0.801170\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:135 | Best split found: feature=customer_id, gain_ratio=0.202665\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:buildNode:630 | Building node root_R_L at depth 2 with 794 samples\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:84 | Starting split evaluation: 794 samples, 26 features\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:117 | Base impurity calculated: 0.798043\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:135 | Best split found: feature=is_premium, gain_ratio=0.146560\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:84 | Starting split evaluation: 373 samples, 26 features\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:117 | Base impurity calculated: 0.989475\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:135 | Best split found: feature=income, gain_ratio=0.193863\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:84 | Starting split evaluation: 167 samples, 26 features\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:117 | Base impurity calculated: 0.857402\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:135 | Best split found: feature=age, gain_ratio=0.217985\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:84 | Starting split evaluation: 112 samples, 26 features\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:117 | Base impurity calculated: 0.981287\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:135 | Best split found: feature=education, gain_ratio=0.501001\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:84 | Starting split evaluation: 206 samples, 26 features\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:117 | Base impurity calculated: 0.748293\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:135 | Best split found: feature=age, gain_ratio=0.414524\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:84 | Starting split evaluation: 75 samples, 26 features\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:117 | Base impurity calculated: 0.978218\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:135 | Best split found: feature=education, gain_ratio=0.495449\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:84 | Starting split evaluation: 421 samples, 26 features\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:117 | Base impurity calculated: 0.352756\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:135 | Best split found: feature=age, gain_ratio=0.128811\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:84 | Starting split evaluation: 135 samples, 26 features\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:117 | Base impurity calculated: 0.736498\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:135 | Best split found: feature=income, gain_ratio=0.367045\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:84 | Starting split evaluation: 52 samples, 26 features\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:117 | Base impurity calculated: 0.995727\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|split.go:FindBestSplit:135 | Best split found: feature=education, gain_ratio=0.503278\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree_builder.go:buildNode:630 | Building node root_R_R at depth 2 with 2 samples\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:UpdateStatistics:578 | Updating tree statistics\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|tree.go:UpdateStatistics:584 | Tree statistics updated: 29 nodes, 18 leaves, depth 7\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|tree_builder.go:BuildTree:456 | Tree construction completed: 29 nodes, 18 leaves, depth 7\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[35mDEBUG\u001b[0m|main.go:createAndBuildTree:238 | Tree building process completed successfully\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:131 | Successfully built tree with 29 nodes, 18 leaves, depth 7\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:CompleteStage:410 | Completed stage: Building Tree (elapsed: 43ms)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:StartStage:394 | Starting stage: Saving Model\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:UpdateProgress:402 | Saving Model: Serializing model to JSON: /tmp/tmp97ly0n6b.dt (elapsed: 43ms)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:UpdateProgress:402 | Saving Model: Model saved successfully (19207 bytes) (elapsed: 43ms)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:CompleteStage:410 | Completed stage: Saving Model (elapsed: 43ms)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:showSuccessMessage:335 | Training completed successfully!\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:showSuccessMessage:336 | Model saved to: /tmp/tmp97ly0n6b.dt\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:showSuccessMessage:337 | Tree statistics: 29 nodes, 18 leaves, depth 7\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handleTrainCommand:145 | Training workflow completed successfully\n", "stderr": "", "execution_time": 0.0742042064666748, "returncode": 0, "tree_structure": {"root": {"type": "decision", "feature": {"name": "income", "type": "numeric", "column_number": 1, "values": [], "numeric_range": {"min": 20077, "max": 149922}, "min": 20077, "max": 149922}, "threshold": 20268.5, "left": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 4}, "samples": 4, "confidence": 1, "impurity": 0}, "right": {"type": "decision", "feature": {"name": "customer_id", "type": "numeric", "column_number": 2, "values": [], "numeric_range": {"min": 106570, "max": 999187}, "min": 106570, "max": 999187}, "threshold": 998927.5, "left": {"type": "decision", "feature": {"name": "is_premium", "type": "numeric", "column_number": 13, "values": [], "numeric_range": {"min": 0, "max": 1}, "max": 1}, "threshold": 0.5, "left": {"type": "decision", "feature": {"name": "income", "type": "numeric", "column_number": 1, "values": [], "numeric_range": {"min": 20077, "max": 149922}, "min": 20077, "max": 149922}, "threshold": 75017.5, "left": {"type": "decision", "feature": {"name": "age", "type": "numeric", "column_number": 0, "values": [], "numeric_range": {"min": 18, "max": 79}, "min": 18, "max": 79}, "threshold": 40.5, "left": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 55}, "samples": 55, "confidence": 1, "impurity": 0}, "right": {"type": "decision", "feature": {"name": "education", "type": "categorical", "column_number": 18, "values": [], "categorical_values": ["PhD", "Master", "Bachelor", "High_School"]}, "threshold": 0, "categories": {"Bachelor": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 40}, "samples": 40, "confidence": 1, "impurity": 0}, "High_School": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 25}, "samples": 25, "confidence": 1, "impurity": 0}, "Master": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 24}, "samples": 24, "confidence": 1, "impurity": 0}, "PhD": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 23}, "samples": 23, "confidence": 1, "impurity": 0}}, "class_distribution": {"No": 65, "Yes": 47}, "samples": 112, "confidence": 0.5803571428571429, "impurity": 0.9812872088817246}, "class_distribution": {"No": 120, "Yes": 47}, "samples": 167, "confidence": 0.718562874251497, "impurity": 0.8574016128221289}, "right": {"type": "decision", "feature": {"name": "age", "type": "numeric", "column_number": 0, "values": [], "numeric_range": {"min": 18, "max": 79}, "min": 18, "max": 79}, "threshold": 40.5, "left": {"type": "decision", "feature": {"name": "education", "type": "categorical", "column_number": 18, "values": [], "categorical_values": ["PhD", "Master", "Bachelor", "High_School"]}, "threshold": 0, "categories": {"Bachelor": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 24}, "samples": 24, "confidence": 1, "impurity": 0}, "High_School": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 20}, "samples": 20, "confidence": 1, "impurity": 0}, "Master": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 15}, "samples": 15, "confidence": 1, "impurity": 0}, "PhD": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 16}, "samples": 16, "confidence": 1, "impurity": 0}}, "class_distribution": {"No": 44, "Yes": 31}, "samples": 75, "confidence": 0.5866666666666667, "impurity": 0.9782176659354248}, "right": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 131}, "samples": 131, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 44, "Yes": 162}, "samples": 206, "confidence": 0.7864077669902912, "impurity": 0.7482932859824889}, "class_distribution": {"No": 164, "Yes": 209}, "samples": 373, "confidence": 0.5603217158176944, "impurity": 0.9894752857719396}, "right": {"type": "decision", "feature": {"name": "age", "type": "numeric", "column_number": 0, "values": [], "numeric_range": {"min": 18, "max": 79}, "min": 18, "max": 79}, "threshold": 40.5, "left": {"type": "decision", "feature": {"name": "income", "type": "numeric", "column_number": 1, "values": [], "numeric_range": {"min": 20077, "max": 149922}, "min": 20077, "max": 149922}, "threshold": 72163.5, "left": {"type": "decision", "feature": {"name": "education", "type": "categorical", "column_number": 18, "values": [], "categorical_values": ["PhD", "Master", "Bachelor", "High_School"]}, "threshold": 0, "categories": {"Bachelor": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 12}, "samples": 12, "confidence": 1, "impurity": 0}, "High_School": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 16}, "samples": 16, "confidence": 1, "impurity": 0}, "Master": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 10}, "samples": 10, "confidence": 1, "impurity": 0}, "PhD": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 14}, "samples": 14, "confidence": 1, "impurity": 0}}, "class_distribution": {"No": 28, "Yes": 24}, "samples": 52, "confidence": 0.5384615384615384, "impurity": 0.9957274520849255}, "right": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 83}, "samples": 83, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 28, "Yes": 107}, "samples": 135, "confidence": 0.7925925925925926, "impurity": 0.7364977795505668}, "right": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 286}, "samples": 286, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 28, "Yes": 393}, "samples": 421, "confidence": 0.9334916864608076, "impurity": 0.3527561375432482}, "class_distribution": {"No": 192, "Yes": 602}, "samples": 794, "confidence": 0.7581863979848866, "impurity": 0.7980432506092013}, "right": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 2}, "samples": 2, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 194, "Yes": 602}, "samples": 796, "confidence": 0.7562814070351759, "impurity": 0.8011696748229913}, "class_distribution": {"No": 198, "Yes": 602}, "samples": 800, "confidence": 0.7525, "impurity": 0.8072916195433455}, "features": {"age": {"name": "age", "type": "numeric", "column_number": 0, "values": []}, "city": {"name": "city", "type": "categorical", "column_number": 10, "values": []}, "color": {"name": "color", "type": "categorical", "column_number": 8, "values": []}, "country": {"name": "country", "type": "categorical", "column_number": 11, "values": []}, "customer_id": {"name": "customer_id", "type": "numeric", "column_number": 2, "values": []}, "department": {"name": "department", "type": "categorical", "column_number": 9, "values": []}, "education": {"name": "education", "type": "categorical", "column_number": 18, "values": []}, "employment_status": {"name": "employment_status", "type": "categorical", "column_number": 17, "values": []}, "gender": {"name": "gender", "type": "categorical", "column_number": 16, "values": []}, "has_subscription": {"name": "has_subscription", "type": "categorical", "column_number": 14, "values": []}, "height_cm": {"name": "height_cm", "type": "numeric", "column_number": 3, "values": []}, "income": {"name": "income", "type": "numeric", "column_number": 1, "values": []}, "income_bracket": {"name": "income_bracket", "type": "categorical", "column_number": 21, "values": []}, "is_premium": {"name": "is_premium", "type": "numeric", "column_number": 13, "values": []}, "last_login": {"name": "last_login", "type": "categorical", "column_number": 24, "values": []}, "owns_car": {"name": "owns_car", "type": "categorical", "column_number": 15, "values": []}, "performance_grade": {"name": "performance_grade", "type": "categorical", "column_number": 22, "values": []}, "preferred_time": {"name": "preferred_time", "type": "categorical", "column_number": 25, "values": []}, "price_dollars": {"name": "price_dollars", "type": "numeric", "column_number": 7, "values": []}, "satisfaction_rating": {"name": "satisfaction_rating", "type": "numeric", "column_number": 19, "values": []}, "score_0_1": {"name": "score_0_1", "type": "numeric", "column_number": 5, "values": []}, "score_0_100": {"name": "score_0_100", "type": "numeric", "column_number": 6, "values": []}, "shirt_size": {"name": "shirt_size", "type": "categorical", "column_number": 20, "values": []}, "signup_date": {"name": "signup_date", "type": "categorical", "column_number": 23, "values": []}, "weight_kg": {"name": "weight_kg", "type": "numeric", "column_number": 4, "values": []}, "zip_code": {"name": "zip_code", "type": "numeric", "column_number": 12, "values": []}}, "features_by_index": [{"name": "age", "type": "numeric", "column_number": 0, "values": []}, {"name": "income", "type": "numeric", "column_number": 1, "values": []}, {"name": "customer_id", "type": "numeric", "column_number": 2, "values": []}, {"name": "height_cm", "type": "numeric", "column_number": 3, "values": []}, {"name": "weight_kg", "type": "numeric", "column_number": 4, "values": []}, {"name": "score_0_1", "type": "numeric", "column_number": 5, "values": []}, {"name": "score_0_100", "type": "numeric", "column_number": 6, "values": []}, {"name": "price_dollars", "type": "numeric", "column_number": 7, "values": []}, {"name": "color", "type": "categorical", "column_number": 8, "values": []}, {"name": "department", "type": "categorical", "column_number": 9, "values": []}, {"name": "city", "type": "categorical", "column_number": 10, "values": []}, {"name": "country", "type": "categorical", "column_number": 11, "values": []}, {"name": "zip_code", "type": "numeric", "column_number": 12, "values": []}, {"name": "is_premium", "type": "numeric", "column_number": 13, "values": []}, {"name": "has_subscription", "type": "categorical", "column_number": 14, "values": []}, {"name": "owns_car", "type": "categorical", "column_number": 15, "values": []}, {"name": "gender", "type": "categorical", "column_number": 16, "values": []}, {"name": "employment_status", "type": "categorical", "column_number": 17, "values": []}, {"name": "education", "type": "categorical", "column_number": 18, "values": []}, {"name": "satisfaction_rating", "type": "numeric", "column_number": 19, "values": []}, {"name": "shirt_size", "type": "categorical", "column_number": 20, "values": []}, {"name": "income_bracket", "type": "categorical", "column_number": 21, "values": []}, {"name": "performance_grade", "type": "categorical", "column_number": 22, "values": []}, {"name": "signup_date", "type": "categorical", "column_number": 23, "values": []}, {"name": "last_login", "type": "categorical", "column_number": 24, "values": []}, {"name": "preferred_time", "type": "categorical", "column_number": 25, "values": []}], "target_type": "categorical", "target_column": "y", "config": {"max_depth": 10, "min_samples": 2, "target_type": "categorical", "criterion": "entropy", "max_features": -1}, "node_count": 29, "leaf_count": 18, "depth": 7}}, "prediction": {"prediction_time": 0.005167961120605469, "stdout": "\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:main:39 | <PERSON><PERSON>berri application starting\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handlePredictCommand:150 | Start prediction command\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:StartStage:394 | Starting stage: Loading data\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handlePredictCommand:162 | Loaded 200 rows with 26 columns\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:CompleteStage:410 | Completed stage: Loading data (elapsed: 1ms)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:StartStage:394 | Starting stage: Making Predictions\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:UpdateProgress:402 | Making Predictions: Processed 0/200 records (elapsed: 1ms)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:UpdateProgress:402 | Making Predictions: Processed 100/200 records (elapsed: 1ms)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:UpdateProgress:402 | Making Predictions: Completed predictions for 200 records (elapsed: 2ms)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:CompleteStage:410 | Completed stage: Making Predictions (elapsed: 2ms)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:StartStage:394 | Starting stage: Saving Results\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:UpdateProgress:402 | Saving Results: Converting results to output format (elapsed: 2ms)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:UpdateProgress:402 | Saving Results: Writing results to /tmp/tmpxxs_yek4.csv (elapsed: 2ms)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:UpdateProgress:402 | Saving Results: Successfully saved 200 predictions (elapsed: 2ms)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:CompleteStage:410 | Completed stage: Saving Results (elapsed: 2ms)\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:showPredictionSuccessMessage:507 | Prediction completed successfully!\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:showPredictionSuccessMessage:508 | Results saved to: /tmp/tmpxxs_yek4.csv\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:showPredictionSuccessMessage:509 | Generated 200 predictions\n\u001b[36m2025-07-31 21:55:11\u001b[0m| \u001b[32mINFO \u001b[0m|main.go:handlePredictCommand:189 | Prediction workflow completed successfully\n", "stderr": "", "returncode": 0, "predictions": "index,prediction,confidence,rule_path\n0,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n1,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age <= 40.500000 & income > 72163.500000 --> LEAF[Yes]]\n2,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age <= 40.500000 --> LEAF[No]]\n3,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n4,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n5,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age <= 40.500000 --> LEAF[No]]\n6,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n7,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age <= 40.500000 & education = Bachelor --> LEAF[No]]\n8,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n9,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n10,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age <= 40.500000 & education = Bachelor --> LEAF[No]]\n11,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age > 40.500000 & education = PhD --> LEAF[Yes]]\n12,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age > 40.500000 & education = Bachelor --> LEAF[No]]\n13,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age <= 40.500000 & income <= 72163.500000 & education = Bachelor --> LEAF[No]]\n14,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n15,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n16,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n17,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age <= 40.500000 & education = High_School --> LEAF[No]]\n18,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age <= 40.500000 & income > 72163.500000 --> LEAF[Yes]]\n19,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n20,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n21,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age > 40.500000 & education = Bachelor --> LEAF[No]]\n22,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age > 40.500000 & education = PhD --> LEAF[Yes]]\n23,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age <= 40.500000 & education = PhD --> LEAF[Yes]]\n24,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n25,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n26,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n27,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age > 40.500000 & education = High_School --> LEAF[No]]\n28,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n29,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n30,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age <= 40.500000 & education = PhD --> LEAF[Yes]]\n31,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n32,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age > 40.500000 & education = Bachelor --> LEAF[No]]\n33,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age > 40.500000 & education = Master --> LEAF[Yes]]\n34,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age <= 40.500000 --> LEAF[No]]\n35,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n36,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age <= 40.500000 & education = Master --> LEAF[Yes]]\n37,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n38,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age > 40.500000 & education = Bachelor --> LEAF[No]]\n39,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n40,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age <= 40.500000 --> LEAF[No]]\n41,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n42,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age <= 40.500000 & income > 72163.500000 --> LEAF[Yes]]\n43,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n44,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age <= 40.500000 & education = High_School --> LEAF[No]]\n45,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age > 40.500000 & education = Bachelor --> LEAF[No]]\n46,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age <= 40.500000 & income > 72163.500000 --> LEAF[Yes]]\n47,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n48,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age <= 40.500000 & income <= 72163.500000 & education = High_School --> LEAF[No]]\n49,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n50,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n51,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n52,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n53,No,1.000000,[income <= 20268.500000 --> LEAF[No]]\n54,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n55,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age > 40.500000 & education = PhD --> LEAF[Yes]]\n56,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n57,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n58,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age <= 40.500000 & education = High_School --> LEAF[No]]\n59,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n60,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age <= 40.500000 & education = Master --> LEAF[Yes]]\n61,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n62,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age <= 40.500000 & education = PhD --> LEAF[Yes]]\n63,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age <= 40.500000 & education = High_School --> LEAF[No]]\n64,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n65,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age <= 40.500000 --> LEAF[No]]\n66,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n67,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n68,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n69,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age > 40.500000 & education = PhD --> LEAF[Yes]]\n70,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n71,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n72,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n73,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age > 40.500000 & education = Master --> LEAF[Yes]]\n74,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age <= 40.500000 & income > 72163.500000 --> LEAF[Yes]]\n75,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age <= 40.500000 & income <= 72163.500000 & education = Master --> LEAF[Yes]]\n76,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n77,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n78,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age > 40.500000 & education = High_School --> LEAF[No]]\n79,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n80,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age > 40.500000 & education = Master --> LEAF[Yes]]\n81,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n82,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n83,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n84,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age <= 40.500000 & education = Bachelor --> LEAF[No]]\n85,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n86,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n87,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age > 40.500000 & education = Master --> LEAF[Yes]]\n88,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age <= 40.500000 & income > 72163.500000 --> LEAF[Yes]]\n89,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n90,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age <= 40.500000 & education = PhD --> LEAF[Yes]]\n91,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n92,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n93,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n94,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n95,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n96,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n97,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age <= 40.500000 --> LEAF[No]]\n98,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age <= 40.500000 & income > 72163.500000 --> LEAF[Yes]]\n99,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age <= 40.500000 & education = PhD --> LEAF[Yes]]\n100,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n101,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n102,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n103,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n104,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age <= 40.500000 & income > 72163.500000 --> LEAF[Yes]]\n105,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n106,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n107,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n108,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n109,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n110,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age <= 40.500000 & income > 72163.500000 --> LEAF[Yes]]\n111,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age <= 40.500000 & income <= 72163.500000 & education = Master --> LEAF[Yes]]\n112,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age > 40.500000 & education = Master --> LEAF[Yes]]\n113,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n114,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age <= 40.500000 --> LEAF[No]]\n115,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n116,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n117,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n118,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n119,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n120,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age > 40.500000 & education = PhD --> LEAF[Yes]]\n121,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n122,No,1.000000,[income <= 20268.500000 --> LEAF[No]]\n123,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age > 40.500000 & education = PhD --> LEAF[Yes]]\n124,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age <= 40.500000 & income > 72163.500000 --> LEAF[Yes]]\n125,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age <= 40.500000 & income <= 72163.500000 & education = High_School --> LEAF[No]]\n126,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age <= 40.500000 & education = Bachelor --> LEAF[No]]\n127,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age <= 40.500000 & income <= 72163.500000 & education = Bachelor --> LEAF[No]]\n128,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age > 40.500000 & education = Bachelor --> LEAF[No]]\n129,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age > 40.500000 & education = Bachelor --> LEAF[No]]\n130,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age > 40.500000 & education = Bachelor --> LEAF[No]]\n131,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age > 40.500000 & education = PhD --> LEAF[Yes]]\n132,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age <= 40.500000 & income <= 72163.500000 & education = Bachelor --> LEAF[No]]\n133,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n134,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n135,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n136,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n137,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age <= 40.500000 & income > 72163.500000 --> LEAF[Yes]]\n138,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n139,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n140,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n141,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n142,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age > 40.500000 & education = PhD --> LEAF[Yes]]\n143,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age <= 40.500000 --> LEAF[No]]\n144,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age > 40.500000 & education = PhD --> LEAF[Yes]]\n145,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n146,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n147,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age <= 40.500000 & education = Bachelor --> LEAF[No]]\n148,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n149,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age > 40.500000 & education = Bachelor --> LEAF[No]]\n150,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age > 40.500000 & education = Bachelor --> LEAF[No]]\n151,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n152,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n153,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age <= 40.500000 --> LEAF[No]]\n154,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age > 40.500000 & education = High_School --> LEAF[No]]\n155,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age > 40.500000 & education = PhD --> LEAF[Yes]]\n156,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age <= 40.500000 & income > 72163.500000 --> LEAF[Yes]]\n157,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age <= 40.500000 --> LEAF[No]]\n158,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age <= 40.500000 & education = High_School --> LEAF[No]]\n159,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age <= 40.500000 & education = Bachelor --> LEAF[No]]\n160,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age <= 40.500000 & education = Master --> LEAF[Yes]]\n161,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age > 40.500000 & education = High_School --> LEAF[No]]\n162,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age <= 40.500000 & education = PhD --> LEAF[Yes]]\n163,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n164,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age > 40.500000 & education = High_School --> LEAF[No]]\n165,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age <= 40.500000 --> LEAF[No]]\n166,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n167,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n168,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n169,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n170,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n171,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n172,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age <= 40.500000 & income > 72163.500000 --> LEAF[Yes]]\n173,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n174,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n175,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n176,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n177,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age <= 40.500000 & education = High_School --> LEAF[No]]\n178,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n179,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age <= 40.500000 & education = Master --> LEAF[Yes]]\n180,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age <= 40.500000 --> LEAF[No]]\n181,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age <= 40.500000 & education = Bachelor --> LEAF[No]]\n182,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n183,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age <= 40.500000 --> LEAF[No]]\n184,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age <= 40.500000 & education = PhD --> LEAF[Yes]]\n185,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n186,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age <= 40.500000 & education = High_School --> LEAF[No]]\n187,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n188,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n189,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age <= 40.500000 & income > 72163.500000 --> LEAF[Yes]]\n190,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age <= 40.500000 & education = Master --> LEAF[Yes]]\n191,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n192,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n193,No,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age <= 40.500000 --> LEAF[No]]\n194,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n195,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income <= 75017.500000 & age > 40.500000 & education = Master --> LEAF[Yes]]\n196,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age > 40.500000 --> LEAF[Yes]]\n197,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age <= 40.500000 & income > 72163.500000 --> LEAF[Yes]]\n198,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium > 0.500000 & age <= 40.500000 & income > 72163.500000 --> LEAF[Yes]]\n199,Yes,1.000000,[income > 20268.500000 & customer_id <= 998927.500000 & is_premium <= 0.500000 & income > 75017.500000 & age > 40.500000 --> LEAF[Yes]]\n"}, "analysis": {"execution_successful": true, "has_tree_structure": true, "output_format": "json", "tree_fields": ["root", "features", "features_by_index", "target_type", "target_column", "config", "node_count", "leaf_count", "depth"], "tree_analysis": {"root_node_present": true, "has_metadata": true, "field_count": 9}}, "metadata": {"test_timestamp": 1753988111.1629748, "implementation": "Go mulber<PERSON>"}}