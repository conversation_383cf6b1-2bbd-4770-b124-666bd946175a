{"params_1": {"parameters": {"max_depth": 10, "min_samples_split": 2, "min_samples_leaf": 1}, "python_result": {"execution_time": 3.875058174133301, "tree_structure": {"root": {"class": "Yes", "statistics": {"sample_size": 800, "class_distribution": {"Yes": 602, "No": 198}, "node_entropy": 0.8072916195433455, "gain_ratio": 0.18196921050590262, "node_depth": 0, "error_rate": null}, "node_type": "branch", "branch_feature": {"name": "income", "data_type": "numeric", "threshold": 20097.0}, "branches": {"<=": {"class": "No", "statistics": {"sample_size": 1, "class_distribution": {"No": 1}, "node_entropy": 0.0, "gain_ratio": null, "node_depth": 1, "error_rate": null}, "node_type": "leaf"}, ">": {"class": "Yes", "statistics": {"sample_size": 799, "class_distribution": {"Yes": 602, "No": 197}, "node_entropy": 0.8057772826122737, "gain_ratio": 0.18249667181446896, "node_depth": 1, "error_rate": null}, "node_type": "branch", "branch_feature": {"name": "income", "data_type": "numeric", "threshold": 20121.5}, "branches": {"<=": {"class": "No", "statistics": {"sample_size": 1, "class_distribution": {"No": 1}, "node_entropy": 0.0, "gain_ratio": null, "node_depth": 2, "error_rate": null}, "node_type": "leaf"}, ">": {"class": "Yes", "statistics": {"sample_size": 798, "class_distribution": {"Yes": 602, "No": 196}, "node_entropy": 0.8042522359016422, "gain_ratio": 0.20147656354842564, "node_depth": 2, "error_rate": null}, "node_type": "branch", "branch_feature": {"name": "customer_id", "data_type": "numeric", "threshold": 998927.5}, "branches": {"<=": {"class": "Yes", "statistics": {"sample_size": 796, "class_distribution": {"Yes": 602, "No": 194}, "node_entropy": 0.8011696748229913, "gain_ratio": 0.18409946103949154, "node_depth": 3, "error_rate": null}, "node_type": "branch", "branch_feature": {"name": "income", "data_type": "numeric", "threshold": 20180.5}, "branches": {"<=": {"class": "No", "statistics": {"sample_size": 1, "class_distribution": {"No": 1}, "node_entropy": 0.0, "gain_ratio": null, "node_depth": 4, "error_rate": null}, "node_type": "leaf"}, ">": {"class": "Yes", "statistics": {"sample_size": 795, "class_distribution": {"Yes": 602, "No": 193}, "node_entropy": 0.7996119890370257, "gain_ratio": 0.18464064825506896, "node_depth": 4, "error_rate": null}, "node_type": "branch", "branch_feature": {"name": "income", "data_type": "numeric", "threshold": 20268.5}, "branches": {"<=": {"class": "No", "statistics": {"sample_size": 1, "class_distribution": {"No": 1}, "node_entropy": 0.0, "gain_ratio": null, "node_depth": 5, "error_rate": null}, "node_type": "leaf"}, ">": {"class": "Yes", "statistics": {"sample_size": 794, "class_distribution": {"Yes": 602, "No": 192}, "node_entropy": 0.7980432506092013, "gain_ratio": 0.1465600619199895, "node_depth": 5, "error_rate": null}, "node_type": "branch", "branch_feature": {"name": "is_premium", "data_type": "numeric", "threshold": 0.5}, "branches": {"<=": {"class": "Yes", "statistics": {"sample_size": 373, "class_distribution": {"Yes": 209, "No": 164}, "node_entropy": 0.9894752857719396, "gain_ratio": 0.19023298860750823, "node_depth": 6, "error_rate": null}, "node_type": "branch", "branch_feature": {"name": "income", "data_type": "numeric", "threshold": 75568.5}, "branches": {"<=": {"class": "No", "statistics": {"sample_size": 170, "class_distribution": {"No": 121, "Yes": 49}, "node_entropy": 0.8664307357149303, "gain_ratio": 0.2189697759733493, "node_depth": 7, "error_rate": null}, "node_type": "branch", "branch_feature": {"name": "education", "data_type": "nominal", "values": ["Master", "High_School", "PhD", "Bachelor"]}, "branches": {"Master": {"class": "Yes", "statistics": {"sample_size": 36, "class_distribution": {"No": 11, "Yes": 25}, "node_entropy": 0.8879763195151349, "gain_ratio": 1.0, "node_depth": 8, "error_rate": null}, "node_type": "branch", "branch_feature": {"name": "age", "data_type": "numeric", "threshold": 39.0}, "branches": {"<=": {"class": "No", "statistics": {"sample_size": 11, "class_distribution": {"No": 11}, "node_entropy": 0.0, "gain_ratio": null, "node_depth": 9, "error_rate": null}, "node_type": "leaf"}, ">": {"class": "Yes", "statistics": {"sample_size": 25, "class_distribution": {"Yes": 25}, "node_entropy": 0.0, "gain_ratio": null, "node_depth": 9, "error_rate": null}, "node_type": "leaf"}}}, "High_School": {"class": "No", "statistics": {"sample_size": 40, "class_distribution": {"No": 40}, "node_entropy": 0.0, "gain_ratio": null, "node_depth": 8, "error_rate": null}, "node_type": "leaf"}, "PhD": {"class": "Yes", "statistics": {"sample_size": 42, "class_distribution": {"No": 18, "Yes": 24}, "node_entropy": 0.9852281360342515, "gain_ratio": 0.8562682786739844, "node_depth": 8, "error_rate": null}, "node_type": "branch", "branch_feature": {"name": "age", "data_type": "numeric", "threshold": 40.5}, "branches": {"<=": {"class": "No", "statistics": {"sample_size": 19, "class_distribution": {"No": 18, "Yes": 1}, "node_entropy": 0.2974722489192896, "gain_ratio": 1.0, "node_depth": 9, "error_rate": null}, "node_type": "branch", "branch_feature": {"name": "age", "data_type": "numeric", "threshold": 18.5}, "branches": {"<=": {"class": "Yes", "statistics": {"sample_size": 1, "class_distribution": {"Yes": 1}, "node_entropy": 0.0, "gain_ratio": null, "node_depth": 10, "error_rate": null}, "node_type": "leaf"}, ">": {"class": "No", "statistics": {"sample_size": 18, "class_distribution": {"No": 18}, "node_entropy": 0.0, "gain_ratio": null, "node_depth": 10, "error_rate": null}, "node_type": "leaf"}}}, ">": {"class": "Yes", "statistics": {"sample_size": 23, "class_distribution": {"Yes": 23}, "node_entropy": 0.0, "gain_ratio": null, "node_depth": 9, "error_rate": null}, "node_type": "leaf"}}}, "Bachelor": {"class": "No", "statistics": {"sample_size": 52, "class_distribution": {"No": 52}, "node_entropy": 0.0, "gain_ratio": null, "node_depth": 8, "error_rate": null}, "node_type": "leaf"}}}, ">": {"class": "Yes", "statistics": {"sample_size": 203, "class_distribution": {"Yes": 160, "No": 43}, "node_entropy": 0.7449522736671061, "gain_ratio": 0.4176946165941081, "node_depth": 7, "error_rate": null}, "node_type": "branch", "branch_feature": {"name": "age", "data_type": "numeric", "threshold": 40.5}, "branches": {"<=": {"class": "No", "statistics": {"sample_size": 73, "class_distribution": {"Yes": 30, "No": 43}, "node_entropy": 0.9770012394218561, "gain_ratio": 0.4947013691681751, "node_depth": 8, "error_rate": null}, "node_type": "branch", "branch_feature": {"name": "education", "data_type": "nominal", "values": ["Master", "High_School", "PhD", "Bachelor"]}, "branches": {"Master": {"class": "Yes", "statistics": {"sample_size": 15, "class_distribution": {"Yes": 15}, "node_entropy": 0.0, "gain_ratio": null, "node_depth": 9, "error_rate": null}, "node_type": "leaf"}, "High_School": {"class": "No", "statistics": {"sample_size": 20, "class_distribution": {"No": 20}, "node_entropy": 0.0, "gain_ratio": null, "node_depth": 9, "error_rate": null}, "node_type": "leaf"}, "PhD": {"class": "Yes", "statistics": {"sample_size": 15, "class_distribution": {"Yes": 15}, "node_entropy": 0.0, "gain_ratio": null, "node_depth": 9, "error_rate": null}, "node_type": "leaf"}, "Bachelor": {"class": "No", "statistics": {"sample_size": 23, "class_distribution": {"No": 23}, "node_entropy": 0.0, "gain_ratio": null, "node_depth": 9, "error_rate": null}, "node_type": "leaf"}}}, ">": {"class": "Yes", "statistics": {"sample_size": 130, "class_distribution": {"Yes": 130}, "node_entropy": 0.0, "gain_ratio": null, "node_depth": 8, "error_rate": null}, "node_type": "leaf"}}}}}, ">": {"class": "Yes", "statistics": {"sample_size": 421, "class_distribution": {"Yes": 393, "No": 28}, "node_entropy": 0.3527561375432482, "gain_ratio": 0.12881073674718907, "node_depth": 6, "error_rate": null}, "node_type": "branch", "branch_feature": {"name": "age", "data_type": "numeric", "threshold": 40.5}, "branches": {"<=": {"class": "Yes", "statistics": {"sample_size": 135, "class_distribution": {"Yes": 107, "No": 28}, "node_entropy": 0.7364977795505668, "gain_ratio": 0.35678226062506624, "node_depth": 7, "error_rate": null}, "node_type": "branch", "branch_feature": {"name": "income", "data_type": "numeric", "threshold": 73267.5}, "branches": {"<=": {"class": "No", "statistics": {"sample_size": 53, "class_distribution": {"No": 28, "Yes": 25}, "node_entropy": 0.9976875760352552, "gain_ratio": 0.5048563692307002, "node_depth": 8, "error_rate": null}, "node_type": "branch", "branch_feature": {"name": "education", "data_type": "nominal", "values": ["High_School", "Bachelor", "PhD", "Master"]}, "branches": {"High_School": {"class": "No", "statistics": {"sample_size": 16, "class_distribution": {"No": 16}, "node_entropy": 0.0, "gain_ratio": null, "node_depth": 9, "error_rate": null}, "node_type": "leaf"}, "Bachelor": {"class": "No", "statistics": {"sample_size": 12, "class_distribution": {"No": 12}, "node_entropy": 0.0, "gain_ratio": null, "node_depth": 9, "error_rate": null}, "node_type": "leaf"}, "PhD": {"class": "Yes", "statistics": {"sample_size": 15, "class_distribution": {"Yes": 15}, "node_entropy": 0.0, "gain_ratio": null, "node_depth": 9, "error_rate": null}, "node_type": "leaf"}, "Master": {"class": "Yes", "statistics": {"sample_size": 10, "class_distribution": {"Yes": 10}, "node_entropy": 0.0, "gain_ratio": null, "node_depth": 9, "error_rate": null}, "node_type": "leaf"}}}, ">": {"class": "Yes", "statistics": {"sample_size": 82, "class_distribution": {"Yes": 82}, "node_entropy": 0.0, "gain_ratio": null, "node_depth": 8, "error_rate": null}, "node_type": "leaf"}}}, ">": {"class": "Yes", "statistics": {"sample_size": 286, "class_distribution": {"Yes": 286}, "node_entropy": 0.0, "gain_ratio": null, "node_depth": 7, "error_rate": null}, "node_type": "leaf"}}}}}}}}}, ">": {"class": "No", "statistics": {"sample_size": 2, "class_distribution": {"No": 2}, "node_entropy": 0.0, "gain_ratio": null, "node_depth": 3, "error_rate": null}, "node_type": "leaf"}}}}}}}}, "parameters": {"max_depth": 10, "min_samples_split": 2, "min_instances_pc": 0.0025}}, "go_result": {"execution_time": 0.029277563095092773, "training": {"root": {"type": "decision", "feature": {"name": "income", "type": "numeric", "column_number": 1, "values": [], "numeric_range": {"min": 20077, "max": 149922}, "min": 20077, "max": 149922}, "threshold": 20268.5, "left": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 4}, "samples": 4, "confidence": 1, "impurity": 0}, "right": {"type": "decision", "feature": {"name": "customer_id", "type": "numeric", "column_number": 2, "values": [], "numeric_range": {"min": 106570, "max": 999187}, "min": 106570, "max": 999187}, "threshold": 998927.5, "left": {"type": "decision", "feature": {"name": "is_premium", "type": "numeric", "column_number": 13, "values": [], "numeric_range": {"min": 0, "max": 1}, "max": 1}, "threshold": 0.5, "left": {"type": "decision", "feature": {"name": "income", "type": "numeric", "column_number": 1, "values": [], "numeric_range": {"min": 20077, "max": 149922}, "min": 20077, "max": 149922}, "threshold": 75017.5, "left": {"type": "decision", "feature": {"name": "age", "type": "numeric", "column_number": 0, "values": [], "numeric_range": {"min": 18, "max": 79}, "min": 18, "max": 79}, "threshold": 40.5, "left": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 55}, "samples": 55, "confidence": 1, "impurity": 0}, "right": {"type": "decision", "feature": {"name": "education", "type": "categorical", "column_number": 18, "values": [], "categorical_values": ["PhD", "Master", "Bachelor", "High_School"]}, "threshold": 0, "categories": {"Bachelor": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 40}, "samples": 40, "confidence": 1, "impurity": 0}, "High_School": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 25}, "samples": 25, "confidence": 1, "impurity": 0}, "Master": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 24}, "samples": 24, "confidence": 1, "impurity": 0}, "PhD": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 23}, "samples": 23, "confidence": 1, "impurity": 0}}, "class_distribution": {"No": 65, "Yes": 47}, "samples": 112, "confidence": 0.5803571428571429, "impurity": 0.9812872088817246}, "class_distribution": {"No": 120, "Yes": 47}, "samples": 167, "confidence": 0.718562874251497, "impurity": 0.8574016128221289}, "right": {"type": "decision", "feature": {"name": "age", "type": "numeric", "column_number": 0, "values": [], "numeric_range": {"min": 18, "max": 79}, "min": 18, "max": 79}, "threshold": 40.5, "left": {"type": "decision", "feature": {"name": "education", "type": "categorical", "column_number": 18, "values": [], "categorical_values": ["PhD", "Master", "Bachelor", "High_School"]}, "threshold": 0, "categories": {"Bachelor": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 24}, "samples": 24, "confidence": 1, "impurity": 0}, "High_School": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 20}, "samples": 20, "confidence": 1, "impurity": 0}, "Master": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 15}, "samples": 15, "confidence": 1, "impurity": 0}, "PhD": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 16}, "samples": 16, "confidence": 1, "impurity": 0}}, "class_distribution": {"No": 44, "Yes": 31}, "samples": 75, "confidence": 0.5866666666666667, "impurity": 0.9782176659354248}, "right": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 131}, "samples": 131, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 44, "Yes": 162}, "samples": 206, "confidence": 0.7864077669902912, "impurity": 0.7482932859824889}, "class_distribution": {"No": 164, "Yes": 209}, "samples": 373, "confidence": 0.5603217158176944, "impurity": 0.9894752857719396}, "right": {"type": "decision", "feature": {"name": "age", "type": "numeric", "column_number": 0, "values": [], "numeric_range": {"min": 18, "max": 79}, "min": 18, "max": 79}, "threshold": 40.5, "left": {"type": "decision", "feature": {"name": "income", "type": "numeric", "column_number": 1, "values": [], "numeric_range": {"min": 20077, "max": 149922}, "min": 20077, "max": 149922}, "threshold": 72163.5, "left": {"type": "decision", "feature": {"name": "education", "type": "categorical", "column_number": 18, "values": [], "categorical_values": ["PhD", "Master", "Bachelor", "High_School"]}, "threshold": 0, "categories": {"Bachelor": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 12}, "samples": 12, "confidence": 1, "impurity": 0}, "High_School": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 16}, "samples": 16, "confidence": 1, "impurity": 0}, "Master": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 10}, "samples": 10, "confidence": 1, "impurity": 0}, "PhD": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 14}, "samples": 14, "confidence": 1, "impurity": 0}}, "class_distribution": {"No": 28, "Yes": 24}, "samples": 52, "confidence": 0.5384615384615384, "impurity": 0.9957274520849255}, "right": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 83}, "samples": 83, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 28, "Yes": 107}, "samples": 135, "confidence": 0.7925925925925926, "impurity": 0.7364977795505668}, "right": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 286}, "samples": 286, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 28, "Yes": 393}, "samples": 421, "confidence": 0.9334916864608076, "impurity": 0.3527561375432482}, "class_distribution": {"No": 192, "Yes": 602}, "samples": 794, "confidence": 0.7581863979848866, "impurity": 0.7980432506092013}, "right": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 2}, "samples": 2, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 194, "Yes": 602}, "samples": 796, "confidence": 0.7562814070351759, "impurity": 0.8011696748229913}, "class_distribution": {"No": 198, "Yes": 602}, "samples": 800, "confidence": 0.7525, "impurity": 0.8072916195433455}, "features": {"age": {"name": "age", "type": "numeric", "column_number": 0, "values": []}, "city": {"name": "city", "type": "categorical", "column_number": 10, "values": []}, "color": {"name": "color", "type": "categorical", "column_number": 8, "values": []}, "country": {"name": "country", "type": "categorical", "column_number": 11, "values": []}, "customer_id": {"name": "customer_id", "type": "numeric", "column_number": 2, "values": []}, "department": {"name": "department", "type": "categorical", "column_number": 9, "values": []}, "education": {"name": "education", "type": "categorical", "column_number": 18, "values": []}, "employment_status": {"name": "employment_status", "type": "categorical", "column_number": 17, "values": []}, "gender": {"name": "gender", "type": "categorical", "column_number": 16, "values": []}, "has_subscription": {"name": "has_subscription", "type": "categorical", "column_number": 14, "values": []}, "height_cm": {"name": "height_cm", "type": "numeric", "column_number": 3, "values": []}, "income": {"name": "income", "type": "numeric", "column_number": 1, "values": []}, "income_bracket": {"name": "income_bracket", "type": "categorical", "column_number": 21, "values": []}, "is_premium": {"name": "is_premium", "type": "numeric", "column_number": 13, "values": []}, "last_login": {"name": "last_login", "type": "categorical", "column_number": 24, "values": []}, "owns_car": {"name": "owns_car", "type": "categorical", "column_number": 15, "values": []}, "performance_grade": {"name": "performance_grade", "type": "categorical", "column_number": 22, "values": []}, "preferred_time": {"name": "preferred_time", "type": "categorical", "column_number": 25, "values": []}, "price_dollars": {"name": "price_dollars", "type": "numeric", "column_number": 7, "values": []}, "satisfaction_rating": {"name": "satisfaction_rating", "type": "numeric", "column_number": 19, "values": []}, "score_0_1": {"name": "score_0_1", "type": "numeric", "column_number": 5, "values": []}, "score_0_100": {"name": "score_0_100", "type": "numeric", "column_number": 6, "values": []}, "shirt_size": {"name": "shirt_size", "type": "categorical", "column_number": 20, "values": []}, "signup_date": {"name": "signup_date", "type": "categorical", "column_number": 23, "values": []}, "weight_kg": {"name": "weight_kg", "type": "numeric", "column_number": 4, "values": []}, "zip_code": {"name": "zip_code", "type": "numeric", "column_number": 12, "values": []}}, "features_by_index": [{"name": "age", "type": "numeric", "column_number": 0, "values": []}, {"name": "income", "type": "numeric", "column_number": 1, "values": []}, {"name": "customer_id", "type": "numeric", "column_number": 2, "values": []}, {"name": "height_cm", "type": "numeric", "column_number": 3, "values": []}, {"name": "weight_kg", "type": "numeric", "column_number": 4, "values": []}, {"name": "score_0_1", "type": "numeric", "column_number": 5, "values": []}, {"name": "score_0_100", "type": "numeric", "column_number": 6, "values": []}, {"name": "price_dollars", "type": "numeric", "column_number": 7, "values": []}, {"name": "color", "type": "categorical", "column_number": 8, "values": []}, {"name": "department", "type": "categorical", "column_number": 9, "values": []}, {"name": "city", "type": "categorical", "column_number": 10, "values": []}, {"name": "country", "type": "categorical", "column_number": 11, "values": []}, {"name": "zip_code", "type": "numeric", "column_number": 12, "values": []}, {"name": "is_premium", "type": "numeric", "column_number": 13, "values": []}, {"name": "has_subscription", "type": "categorical", "column_number": 14, "values": []}, {"name": "owns_car", "type": "categorical", "column_number": 15, "values": []}, {"name": "gender", "type": "categorical", "column_number": 16, "values": []}, {"name": "employment_status", "type": "categorical", "column_number": 17, "values": []}, {"name": "education", "type": "categorical", "column_number": 18, "values": []}, {"name": "satisfaction_rating", "type": "numeric", "column_number": 19, "values": []}, {"name": "shirt_size", "type": "categorical", "column_number": 20, "values": []}, {"name": "income_bracket", "type": "categorical", "column_number": 21, "values": []}, {"name": "performance_grade", "type": "categorical", "column_number": 22, "values": []}, {"name": "signup_date", "type": "categorical", "column_number": 23, "values": []}, {"name": "last_login", "type": "categorical", "column_number": 24, "values": []}, {"name": "preferred_time", "type": "categorical", "column_number": 25, "values": []}], "target_type": "categorical", "target_column": "y", "config": {"max_depth": 10, "min_samples": 2, "target_type": "categorical", "criterion": "entropy", "max_features": -1}, "node_count": 29, "leaf_count": 18, "depth": 7}, "parameters": {"max_depth": 10, "min_samples_split": 2, "min_samples_leaf": 1}}, "comparison": {"python_stats": {"total_nodes": 39, "leaf_nodes": 23, "decision_nodes": 16, "max_depth": 10, "avg_leaf_depth": 7.521739130434782, "leaf_depths": [1, 2, 4, 5, 9, 9, 8, 10, 10, 9, 8, 9, 9, 9, 9, 8, 9, 9, 9, 9, 8, 7, 3]}, "go_stats": {"total_nodes": 17, "leaf_nodes": 6, "decision_nodes": 11, "max_depth": 5, "avg_leaf_depth": 3.6666666666666665, "leaf_depths": [1, 5, 5, 5, 4, 2]}, "structural_differences": {"node_count_diff": 22, "leaf_count_diff": 17, "depth_diff": 5, "avg_depth_diff": 3.8550724637681157}, "decision_rules": {"python_rule_count": 23, "go_rule_count": 6, "python_rules": [{"path": " -> unknown <= None", "prediction": "No", "samples": 1, "depth": 1}, {"path": " -> unknown > None -> unknown <= None", "prediction": "No", "samples": 1, "depth": 2}, {"path": " -> unknown > None -> unknown > None -> unknown <= None -> unknown <= None", "prediction": "No", "samples": 1, "depth": 4}, {"path": " -> unknown > None -> unknown > None -> unknown <= None -> unknown > None -> unknown <= None", "prediction": "No", "samples": 1, "depth": 5}, {"path": " -> unknown > None -> unknown > None -> unknown <= None -> unknown > None -> unknown > None -> unknown <= None -> unknown <= None -> unknown Master None -> unknown <= None", "prediction": "No", "samples": 11, "depth": 9}], "go_rules": [{"path": " -> income <= 20268.5", "prediction": "No", "samples": 4, "depth": 1}, {"path": " -> income > 20268.5 -> customer_id <= 998927.5 -> is_premium <= 0.5 -> income <= 75017.5 -> age <= 40.5", "prediction": "No", "samples": 55, "depth": 5}, {"path": " -> income > 20268.5 -> customer_id <= 998927.5 -> is_premium <= 0.5 -> income > 75017.5 -> age > 40.5", "prediction": "Yes", "samples": 131, "depth": 5}, {"path": " -> income > 20268.5 -> customer_id <= 998927.5 -> is_premium > 0.5 -> age <= 40.5 -> income > 72163.5", "prediction": "Yes", "samples": 83, "depth": 5}, {"path": " -> income > 20268.5 -> customer_id <= 998927.5 -> is_premium > 0.5 -> age > 40.5", "prediction": "Yes", "samples": 286, "depth": 4}]}, "performance": {"python_time": 3.875058174133301, "go_time": 0.029277563095092773, "speed_ratio": 132.3558986636699}}}, "params_2": {"parameters": {"max_depth": 5, "min_samples_split": 10, "min_samples_leaf": 5}, "python_result": {"execution_time": 1.6626815795898438, "tree_structure": {"root": {"class": "Yes", "statistics": {"sample_size": 800, "class_distribution": {"Yes": 602, "No": 198}, "node_entropy": 0.8072916195433455, "gain_ratio": 0.18196921050590262, "node_depth": 0, "error_rate": null}, "node_type": "branch", "branch_feature": {"name": "income", "data_type": "numeric", "threshold": 20097.0}, "branches": {"<=": {"class": "No", "statistics": {"sample_size": 1, "class_distribution": {"No": 1}, "node_entropy": 0.0, "gain_ratio": null, "node_depth": 1, "error_rate": null}, "node_type": "leaf"}, ">": {"class": "Yes", "statistics": {"sample_size": 799, "class_distribution": {"Yes": 602, "No": 197}, "node_entropy": 0.8057772826122737, "gain_ratio": 0.18249667181446896, "node_depth": 1, "error_rate": null}, "node_type": "branch", "branch_feature": {"name": "income", "data_type": "numeric", "threshold": 20121.5}, "branches": {"<=": {"class": "No", "statistics": {"sample_size": 1, "class_distribution": {"No": 1}, "node_entropy": 0.0, "gain_ratio": null, "node_depth": 2, "error_rate": null}, "node_type": "leaf"}, ">": {"class": "Yes", "statistics": {"sample_size": 798, "class_distribution": {"Yes": 602, "No": 196}, "node_entropy": 0.8042522359016422, "gain_ratio": 0.20147656354842564, "node_depth": 2, "error_rate": null}, "node_type": "branch", "branch_feature": {"name": "customer_id", "data_type": "numeric", "threshold": 998927.5}, "branches": {"<=": {"class": "Yes", "statistics": {"sample_size": 796, "class_distribution": {"Yes": 602, "No": 194}, "node_entropy": 0.8011696748229913, "gain_ratio": 0.18409946103949154, "node_depth": 3, "error_rate": null}, "node_type": "branch", "branch_feature": {"name": "income", "data_type": "numeric", "threshold": 20180.5}, "branches": {"<=": {"class": "No", "statistics": {"sample_size": 1, "class_distribution": {"No": 1}, "node_entropy": 0.0, "gain_ratio": null, "node_depth": 4, "error_rate": null}, "node_type": "leaf"}, ">": {"class": "Yes", "statistics": {"sample_size": 795, "class_distribution": {"Yes": 602, "No": 193}, "node_entropy": 0.7996119890370257, "gain_ratio": 0.18464064825506896, "node_depth": 4, "error_rate": null}, "node_type": "branch", "branch_feature": {"name": "income", "data_type": "numeric", "threshold": 20268.5}, "branches": {"<=": {"class": "No", "statistics": {"sample_size": 1, "class_distribution": {"No": 1}, "node_entropy": 0.0, "gain_ratio": null, "node_depth": 5, "error_rate": null}, "node_type": "leaf"}, ">": {"class": "Yes", "statistics": {"sample_size": 794, "class_distribution": {"Yes": 602, "No": 192}, "node_entropy": 0.7980432506092013, "gain_ratio": null, "node_depth": 5, "error_rate": null}, "node_type": "leaf"}}}}}, ">": {"class": "No", "statistics": {"sample_size": 2, "class_distribution": {"No": 2}, "node_entropy": 0.0, "gain_ratio": null, "node_depth": 3, "error_rate": null}, "node_type": "leaf"}}}}}}}}, "parameters": {"max_depth": 5, "min_samples_split": 10, "min_instances_pc": 0.0125}}, "go_result": {"execution_time": 0.02212667465209961, "training": {"root": {"type": "decision", "feature": {"name": "is_premium", "type": "numeric", "column_number": 13, "values": [], "numeric_range": {"min": 0, "max": 1}, "max": 1}, "threshold": 0.5, "left": {"type": "decision", "feature": {"name": "income", "type": "numeric", "column_number": 1, "values": [], "numeric_range": {"min": 20077, "max": 149922}, "min": 20077, "max": 149922}, "threshold": 75017.5, "left": {"type": "decision", "feature": {"name": "education", "type": "categorical", "column_number": 18, "values": [], "categorical_values": ["PhD", "Master", "Bachelor", "High_School"]}, "threshold": 0, "categories": {"Bachelor": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 54}, "samples": 54, "confidence": 1, "impurity": 0}, "High_School": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 40}, "samples": 40, "confidence": 1, "impurity": 0}, "Master": {"type": "decision", "feature": {"name": "age", "type": "numeric", "column_number": 0, "values": [], "numeric_range": {"min": 18, "max": 79}, "min": 18, "max": 79}, "threshold": 39, "left": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 11}, "samples": 11, "confidence": 1, "impurity": 0}, "right": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 24}, "samples": 24, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 11, "Yes": 24}, "samples": 35, "confidence": 0.6857142857142857, "impurity": 0.8980587934501658}, "PhD": {"type": "decision", "feature": {"name": "age", "type": "numeric", "column_number": 0, "values": [], "numeric_range": {"min": 18, "max": 79}, "min": 18, "max": 79}, "threshold": 40.5, "left": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 18}, "samples": 18, "confidence": 1, "impurity": 0}, "right": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 23}, "samples": 23, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 18, "Yes": 23}, "samples": 41, "confidence": 0.5609756097560976, "impurity": 0.9892452969285004}}, "class_distribution": {"No": 123, "Yes": 47}, "samples": 170, "confidence": 0.7235294117647059, "impurity": 0.8505970526931401}, "right": {"type": "decision", "feature": {"name": "age", "type": "numeric", "column_number": 0, "values": [], "numeric_range": {"min": 18, "max": 79}, "min": 18, "max": 79}, "threshold": 40.5, "left": {"type": "decision", "feature": {"name": "education", "type": "categorical", "column_number": 18, "values": [], "categorical_values": ["PhD", "Master", "Bachelor", "High_School"]}, "threshold": 0, "categories": {"Bachelor": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 24}, "samples": 24, "confidence": 1, "impurity": 0}, "High_School": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 21}, "samples": 21, "confidence": 1, "impurity": 0}, "Master": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 15}, "samples": 15, "confidence": 1, "impurity": 0}, "PhD": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 16}, "samples": 16, "confidence": 1, "impurity": 0}}, "class_distribution": {"No": 45, "Yes": 31}, "samples": 76, "confidence": 0.5921052631578947, "impurity": 0.9753817903274211}, "right": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 131}, "samples": 131, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 45, "Yes": 162}, "samples": 207, "confidence": 0.782608695652174, "impurity": 0.7553754125614287}, "class_distribution": {"No": 168, "Yes": 209}, "samples": 377, "confidence": 0.5543766578249337, "impurity": 0.9914515206115391}, "right": {"type": "decision", "feature": {"name": "age", "type": "numeric", "column_number": 0, "values": [], "numeric_range": {"min": 18, "max": 79}, "min": 18, "max": 79}, "threshold": 40.5, "left": {"type": "decision", "feature": {"name": "income", "type": "numeric", "column_number": 1, "values": [], "numeric_range": {"min": 20077, "max": 149922}, "min": 20077, "max": 149922}, "threshold": 72163.5, "left": {"type": "decision", "feature": {"name": "education", "type": "categorical", "column_number": 18, "values": [], "categorical_values": ["PhD", "Master", "Bachelor", "High_School"]}, "threshold": 0, "categories": {"Bachelor": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 13}, "samples": 13, "confidence": 1, "impurity": 0}, "High_School": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 17}, "samples": 17, "confidence": 1, "impurity": 0}, "Master": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 10}, "samples": 10, "confidence": 1, "impurity": 0}, "PhD": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 14}, "samples": 14, "confidence": 1, "impurity": 0}}, "class_distribution": {"No": 30, "Yes": 24}, "samples": 54, "confidence": 0.5555555555555556, "impurity": 0.9910760598382222}, "right": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 83}, "samples": 83, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 30, "Yes": 107}, "samples": 137, "confidence": 0.781021897810219, "impurity": 0.7582971529373276}, "right": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 286}, "samples": 286, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 30, "Yes": 393}, "samples": 423, "confidence": 0.9290780141843972, "impurity": 0.369354939985463}, "class_distribution": {"No": 198, "Yes": 602}, "samples": 800, "confidence": 0.7525, "impurity": 0.8072916195433455}, "features": {"age": {"name": "age", "type": "numeric", "column_number": 0, "values": []}, "city": {"name": "city", "type": "categorical", "column_number": 10, "values": []}, "color": {"name": "color", "type": "categorical", "column_number": 8, "values": []}, "country": {"name": "country", "type": "categorical", "column_number": 11, "values": []}, "customer_id": {"name": "customer_id", "type": "numeric", "column_number": 2, "values": []}, "department": {"name": "department", "type": "categorical", "column_number": 9, "values": []}, "education": {"name": "education", "type": "categorical", "column_number": 18, "values": []}, "employment_status": {"name": "employment_status", "type": "categorical", "column_number": 17, "values": []}, "gender": {"name": "gender", "type": "categorical", "column_number": 16, "values": []}, "has_subscription": {"name": "has_subscription", "type": "categorical", "column_number": 14, "values": []}, "height_cm": {"name": "height_cm", "type": "numeric", "column_number": 3, "values": []}, "income": {"name": "income", "type": "numeric", "column_number": 1, "values": []}, "income_bracket": {"name": "income_bracket", "type": "categorical", "column_number": 21, "values": []}, "is_premium": {"name": "is_premium", "type": "numeric", "column_number": 13, "values": []}, "last_login": {"name": "last_login", "type": "categorical", "column_number": 24, "values": []}, "owns_car": {"name": "owns_car", "type": "categorical", "column_number": 15, "values": []}, "performance_grade": {"name": "performance_grade", "type": "categorical", "column_number": 22, "values": []}, "preferred_time": {"name": "preferred_time", "type": "categorical", "column_number": 25, "values": []}, "price_dollars": {"name": "price_dollars", "type": "numeric", "column_number": 7, "values": []}, "satisfaction_rating": {"name": "satisfaction_rating", "type": "numeric", "column_number": 19, "values": []}, "score_0_1": {"name": "score_0_1", "type": "numeric", "column_number": 5, "values": []}, "score_0_100": {"name": "score_0_100", "type": "numeric", "column_number": 6, "values": []}, "shirt_size": {"name": "shirt_size", "type": "categorical", "column_number": 20, "values": []}, "signup_date": {"name": "signup_date", "type": "categorical", "column_number": 23, "values": []}, "weight_kg": {"name": "weight_kg", "type": "numeric", "column_number": 4, "values": []}, "zip_code": {"name": "zip_code", "type": "numeric", "column_number": 12, "values": []}}, "features_by_index": [{"name": "age", "type": "numeric", "column_number": 0, "values": []}, {"name": "income", "type": "numeric", "column_number": 1, "values": []}, {"name": "customer_id", "type": "numeric", "column_number": 2, "values": []}, {"name": "height_cm", "type": "numeric", "column_number": 3, "values": []}, {"name": "weight_kg", "type": "numeric", "column_number": 4, "values": []}, {"name": "score_0_1", "type": "numeric", "column_number": 5, "values": []}, {"name": "score_0_100", "type": "numeric", "column_number": 6, "values": []}, {"name": "price_dollars", "type": "numeric", "column_number": 7, "values": []}, {"name": "color", "type": "categorical", "column_number": 8, "values": []}, {"name": "department", "type": "categorical", "column_number": 9, "values": []}, {"name": "city", "type": "categorical", "column_number": 10, "values": []}, {"name": "country", "type": "categorical", "column_number": 11, "values": []}, {"name": "zip_code", "type": "numeric", "column_number": 12, "values": []}, {"name": "is_premium", "type": "numeric", "column_number": 13, "values": []}, {"name": "has_subscription", "type": "categorical", "column_number": 14, "values": []}, {"name": "owns_car", "type": "categorical", "column_number": 15, "values": []}, {"name": "gender", "type": "categorical", "column_number": 16, "values": []}, {"name": "employment_status", "type": "categorical", "column_number": 17, "values": []}, {"name": "education", "type": "categorical", "column_number": 18, "values": []}, {"name": "satisfaction_rating", "type": "numeric", "column_number": 19, "values": []}, {"name": "shirt_size", "type": "categorical", "column_number": 20, "values": []}, {"name": "income_bracket", "type": "categorical", "column_number": 21, "values": []}, {"name": "performance_grade", "type": "categorical", "column_number": 22, "values": []}, {"name": "signup_date", "type": "categorical", "column_number": 23, "values": []}, {"name": "last_login", "type": "categorical", "column_number": 24, "values": []}, {"name": "preferred_time", "type": "categorical", "column_number": 25, "values": []}], "target_type": "categorical", "target_column": "y", "config": {"max_depth": 5, "min_samples": 10, "target_type": "categorical", "criterion": "entropy", "max_features": -1}, "node_count": 27, "leaf_count": 17, "depth": 5}, "parameters": {"max_depth": 5, "min_samples_split": 10, "min_samples_leaf": 5}}, "comparison": {"python_stats": {"total_nodes": 11, "leaf_nodes": 6, "decision_nodes": 5, "max_depth": 5, "avg_leaf_depth": 3.3333333333333335, "leaf_depths": [1, 2, 4, 5, 5, 3]}, "go_stats": {"total_nodes": 11, "leaf_nodes": 3, "decision_nodes": 8, "max_depth": 3, "avg_leaf_depth": 2.6666666666666665, "leaf_depths": [3, 3, 2]}, "structural_differences": {"node_count_diff": 0, "leaf_count_diff": 3, "depth_diff": 2, "avg_depth_diff": 0.666666666666667}, "decision_rules": {"python_rule_count": 6, "go_rule_count": 3, "python_rules": [{"path": " -> unknown <= None", "prediction": "No", "samples": 1, "depth": 1}, {"path": " -> unknown > None -> unknown <= None", "prediction": "No", "samples": 1, "depth": 2}, {"path": " -> unknown > None -> unknown > None -> unknown <= None -> unknown <= None", "prediction": "No", "samples": 1, "depth": 4}, {"path": " -> unknown > None -> unknown > None -> unknown <= None -> unknown > None -> unknown <= None", "prediction": "No", "samples": 1, "depth": 5}, {"path": " -> unknown > None -> unknown > None -> unknown <= None -> unknown > None -> unknown > None", "prediction": "Yes", "samples": 794, "depth": 5}], "go_rules": [{"path": " -> is_premium <= 0.5 -> income > 75017.5 -> age > 40.5", "prediction": "Yes", "samples": 131, "depth": 3}, {"path": " -> is_premium > 0.5 -> age <= 40.5 -> income > 72163.5", "prediction": "Yes", "samples": 83, "depth": 3}, {"path": " -> is_premium > 0.5 -> age > 40.5", "prediction": "Yes", "samples": 286, "depth": 2}]}, "performance": {"python_time": 1.6626815795898438, "go_time": 0.02212667465209961, "speed_ratio": 75.14376225675065}}}}