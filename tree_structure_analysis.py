#!/usr/bin/env python3
"""
Comprehensive analysis of decision tree structures comparing Python and Go implementations.
Focus on tree topology, decision paths, and node characteristics.
"""

import json
from typing import Dict, <PERSON>, Tuple, Any
from collections import defaultdict, Counter

def load_tree_data():
    """Load tree structures from both implementations."""
    with open("analysis_output/python/tree_structure.json", 'r') as f:
        python_data = json.load(f)
    
    with open("analysis_output/go/test_results.json", 'r') as f:
        go_data = json.load(f)
    
    python_tree = python_data["tree_structure"]["root"]
    go_tree = go_data["training"]["tree_structure"]["root"]
    
    return python_tree, go_tree

def analyze_tree_structure(tree, implementation_name, is_go_format=False):
    """Analyze the structure and characteristics of a decision tree."""
    
    analysis = {
        "implementation": implementation_name,
        "total_nodes": 0,
        "leaf_nodes": 0,
        "decision_nodes": 0,
        "max_depth": 0,
        "features_used": Counter(),
        "split_thresholds": defaultdict(list),
        "node_sample_sizes": [],
        "leaf_predictions": Counter(),
        "decision_paths": [],
        "depth_distribution": Counter(),
        "feature_importance_by_depth": defaultdict(Counter)
    }
    
    def traverse_node(node, depth=0, path="root"):
        analysis["total_nodes"] += 1
        analysis["max_depth"] = max(analysis["max_depth"], depth)
        analysis["depth_distribution"][depth] += 1
        
        # Determine if leaf node
        if is_go_format:
            is_leaf = node.get("type") == "leaf"
            node_type = node.get("type", "unknown")
        else:
            is_leaf = node.get("is_leaf", False) or node.get("node_type") == "leaf"
            node_type = node.get("node_type", "unknown")
        
        # Extract sample size
        if is_go_format:
            samples = node.get("samples", 0)
        else:
            samples = node.get("statistics", {}).get("sample_size", 0)
        
        if samples > 0:
            analysis["node_sample_sizes"].append(samples)
        
        if is_leaf:
            analysis["leaf_nodes"] += 1
            
            # Extract prediction
            prediction = node.get("prediction") or node.get("class")
            if prediction:
                analysis["leaf_predictions"][prediction] += 1
            
            # Record decision path
            analysis["decision_paths"].append({
                "path": path,
                "depth": depth,
                "prediction": prediction,
                "samples": samples,
                "confidence": node.get("confidence", 0)
            })
        else:
            analysis["decision_nodes"] += 1
            
            # Extract feature information
            feature_info = node.get("feature", {})
            feature_name = feature_info.get("name")
            
            if feature_name:
                analysis["features_used"][feature_name] += 1
                analysis["feature_importance_by_depth"][depth][feature_name] += 1
                
                # Extract threshold
                if is_go_format:
                    threshold = node.get("threshold")
                else:
                    threshold = feature_info.get("threshold")
                
                if threshold is not None:
                    analysis["split_thresholds"][feature_name].append(threshold)
            
            # Recursively analyze children
            if is_go_format:
                if "left" in node:
                    traverse_node(node["left"], depth + 1, f"{path}.left")
                if "right" in node:
                    traverse_node(node["right"], depth + 1, f"{path}.right")
            else:
                branches = node.get("branches", {})
                for branch_condition, child_node in branches.items():
                    traverse_node(child_node, depth + 1, f"{path}.{branch_condition}")
    
    traverse_node(tree)
    return analysis

def compare_decision_paths(python_analysis, go_analysis):
    """Compare the decision paths between implementations."""
    
    comparison = {
        "path_count": {
            "python": len(python_analysis["decision_paths"]),
            "go": len(go_analysis["decision_paths"])
        },
        "prediction_distribution": {
            "python": dict(python_analysis["leaf_predictions"]),
            "go": dict(go_analysis["leaf_predictions"])
        },
        "depth_analysis": {
            "python_depths": [p["depth"] for p in python_analysis["decision_paths"]],
            "go_depths": [p["depth"] for p in go_analysis["decision_paths"]]
        }
    }
    
    # Calculate average depths
    if comparison["depth_analysis"]["python_depths"]:
        comparison["avg_leaf_depth"] = {
            "python": sum(comparison["depth_analysis"]["python_depths"]) / len(comparison["depth_analysis"]["python_depths"]),
            "go": sum(comparison["depth_analysis"]["go_depths"]) / len(comparison["depth_analysis"]["go_depths"])
        }
    
    return comparison

def analyze_feature_usage(python_analysis, go_analysis):
    """Analyze how features are used in both trees."""
    
    python_features = set(python_analysis["features_used"].keys())
    go_features = set(go_analysis["features_used"].keys())
    
    feature_comparison = {
        "common_features": list(python_features & go_features),
        "python_only": list(python_features - go_features),
        "go_only": list(go_features - python_features),
        "feature_usage_frequency": {
            "python": dict(python_analysis["features_used"]),
            "go": dict(go_analysis["features_used"])
        },
        "split_threshold_comparison": {}
    }
    
    # Compare thresholds for common features
    for feature in feature_comparison["common_features"]:
        python_thresholds = python_analysis["split_thresholds"].get(feature, [])
        go_thresholds = go_analysis["split_thresholds"].get(feature, [])
        
        feature_comparison["split_threshold_comparison"][feature] = {
            "python_thresholds": python_thresholds,
            "go_thresholds": go_thresholds,
            "threshold_match": set(python_thresholds) == set(go_thresholds)
        }
    
    return feature_comparison

def generate_tree_topology_report(python_analysis, go_analysis):
    """Generate a comprehensive report on tree topology."""
    
    path_comparison = compare_decision_paths(python_analysis, go_analysis)
    feature_comparison = analyze_feature_usage(python_analysis, go_analysis)
    
    report = {
        "tree_statistics": {
            "python": {
                "total_nodes": python_analysis["total_nodes"],
                "leaf_nodes": python_analysis["leaf_nodes"],
                "decision_nodes": python_analysis["decision_nodes"],
                "max_depth": python_analysis["max_depth"],
                "avg_samples_per_node": sum(python_analysis["node_sample_sizes"]) / len(python_analysis["node_sample_sizes"]) if python_analysis["node_sample_sizes"] else 0
            },
            "go": {
                "total_nodes": go_analysis["total_nodes"],
                "leaf_nodes": go_analysis["leaf_nodes"],
                "decision_nodes": go_analysis["decision_nodes"],
                "max_depth": go_analysis["max_depth"],
                "avg_samples_per_node": sum(go_analysis["node_sample_sizes"]) / len(go_analysis["node_sample_sizes"]) if go_analysis["node_sample_sizes"] else 0
            }
        },
        "decision_paths": path_comparison,
        "feature_analysis": feature_comparison,
        "depth_distribution": {
            "python": dict(python_analysis["depth_distribution"]),
            "go": dict(go_analysis["depth_distribution"])
        },
        "structural_equivalence": {
            "same_node_count": python_analysis["total_nodes"] == go_analysis["total_nodes"],
            "same_leaf_count": python_analysis["leaf_nodes"] == go_analysis["leaf_nodes"],
            "same_max_depth": python_analysis["max_depth"] == go_analysis["max_depth"],
            "same_predictions": python_analysis["leaf_predictions"] == go_analysis["leaf_predictions"]
        }
    }
    
    return report

def main():
    """Main analysis function."""
    print("="*60)
    print("DECISION TREE STRUCTURE ANALYSIS")
    print("="*60)
    
    # Load tree data
    python_tree, go_tree = load_tree_data()
    
    # Analyze both trees
    python_analysis = analyze_tree_structure(python_tree, "Python", is_go_format=False)
    go_analysis = analyze_tree_structure(go_tree, "Go", is_go_format=True)
    
    # Generate comprehensive report
    report = generate_tree_topology_report(python_analysis, go_analysis)
    
    # Save detailed analysis
    with open("analysis_output/tree_structure_analysis.json", "w") as f:
        json.dump({
            "python_analysis": python_analysis,
            "go_analysis": go_analysis,
            "comparison_report": report
        }, f, indent=2, default=str)
    
    # Print summary
    print(f"\n--- TREE TOPOLOGY COMPARISON ---")
    print(f"Python: {python_analysis['total_nodes']} nodes ({python_analysis['leaf_nodes']} leaves, depth {python_analysis['max_depth']})")
    print(f"Go:     {go_analysis['total_nodes']} nodes ({go_analysis['leaf_nodes']} leaves, depth {go_analysis['max_depth']})")
    print(f"Structural Match: {report['structural_equivalence']['same_node_count'] and report['structural_equivalence']['same_leaf_count']}")
    
    print(f"\n--- FEATURE USAGE ---")
    print(f"Common features: {len(report['feature_analysis']['common_features'])}")
    print(f"Python-only: {len(report['feature_analysis']['python_only'])}")
    print(f"Go-only: {len(report['feature_analysis']['go_only'])}")
    
    print(f"\n--- DECISION PATHS ---")
    print(f"Python paths: {len(python_analysis['decision_paths'])}")
    print(f"Go paths: {len(go_analysis['decision_paths'])}")
    
    if 'avg_leaf_depth' in report['decision_paths']:
        print(f"Avg leaf depth - Python: {report['decision_paths']['avg_leaf_depth']['python']:.1f}, Go: {report['decision_paths']['avg_leaf_depth']['go']:.1f}")
    
    print(f"\n✓ Detailed analysis saved to analysis_output/tree_structure_analysis.json")

if __name__ == "__main__":
    main()
