{"root": {"type": "decision", "feature": {"name": "is_premium", "type": "numeric", "column_number": 13, "values": [], "numeric_range": {"min": 0, "max": 1}, "max": 1}, "threshold": 0.5, "left": {"type": "decision", "feature": {"name": "income", "type": "numeric", "column_number": 1, "values": [], "numeric_range": {"min": 20077, "max": 149922}, "min": 20077, "max": 149922}, "threshold": 75017.5, "left": {"type": "decision", "feature": {"name": "education", "type": "categorical", "column_number": 18, "values": [], "categorical_values": ["PhD", "Master", "Bachelor", "High_School"]}, "threshold": 0, "categories": {"Bachelor": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 54}, "samples": 54, "confidence": 1, "impurity": 0}, "High_School": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 40}, "samples": 40, "confidence": 1, "impurity": 0}, "Master": {"type": "decision", "feature": {"name": "age", "type": "numeric", "column_number": 0, "values": [], "numeric_range": {"min": 18, "max": 79}, "min": 18, "max": 79}, "threshold": 39, "left": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 11}, "samples": 11, "confidence": 1, "impurity": 0}, "right": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 24}, "samples": 24, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 11, "Yes": 24}, "samples": 35, "confidence": 0.6857142857142857, "impurity": 0.8980587934501658}, "PhD": {"type": "decision", "feature": {"name": "age", "type": "numeric", "column_number": 0, "values": [], "numeric_range": {"min": 18, "max": 79}, "min": 18, "max": 79}, "threshold": 40.5, "left": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 18}, "samples": 18, "confidence": 1, "impurity": 0}, "right": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 23}, "samples": 23, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 18, "Yes": 23}, "samples": 41, "confidence": 0.5609756097560976, "impurity": 0.9892452969285004}}, "class_distribution": {"No": 123, "Yes": 47}, "samples": 170, "confidence": 0.7235294117647059, "impurity": 0.8505970526931401}, "right": {"type": "decision", "feature": {"name": "age", "type": "numeric", "column_number": 0, "values": [], "numeric_range": {"min": 18, "max": 79}, "min": 18, "max": 79}, "threshold": 40.5, "left": {"type": "decision", "feature": {"name": "education", "type": "categorical", "column_number": 18, "values": [], "categorical_values": ["PhD", "Master", "Bachelor", "High_School"]}, "threshold": 0, "categories": {"Bachelor": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 24}, "samples": 24, "confidence": 1, "impurity": 0}, "High_School": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 21}, "samples": 21, "confidence": 1, "impurity": 0}, "Master": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 15}, "samples": 15, "confidence": 1, "impurity": 0}, "PhD": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 16}, "samples": 16, "confidence": 1, "impurity": 0}}, "class_distribution": {"No": 45, "Yes": 31}, "samples": 76, "confidence": 0.5921052631578947, "impurity": 0.9753817903274211}, "right": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 131}, "samples": 131, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 45, "Yes": 162}, "samples": 207, "confidence": 0.782608695652174, "impurity": 0.7553754125614287}, "class_distribution": {"No": 168, "Yes": 209}, "samples": 377, "confidence": 0.5543766578249337, "impurity": 0.9914515206115391}, "right": {"type": "decision", "feature": {"name": "age", "type": "numeric", "column_number": 0, "values": [], "numeric_range": {"min": 18, "max": 79}, "min": 18, "max": 79}, "threshold": 40.5, "left": {"type": "decision", "feature": {"name": "income", "type": "numeric", "column_number": 1, "values": [], "numeric_range": {"min": 20077, "max": 149922}, "min": 20077, "max": 149922}, "threshold": 72163.5, "left": {"type": "decision", "feature": {"name": "education", "type": "categorical", "column_number": 18, "values": [], "categorical_values": ["PhD", "Master", "Bachelor", "High_School"]}, "threshold": 0, "categories": {"Bachelor": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 13}, "samples": 13, "confidence": 1, "impurity": 0}, "High_School": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 17}, "samples": 17, "confidence": 1, "impurity": 0}, "Master": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 10}, "samples": 10, "confidence": 1, "impurity": 0}, "PhD": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 14}, "samples": 14, "confidence": 1, "impurity": 0}}, "class_distribution": {"No": 30, "Yes": 24}, "samples": 54, "confidence": 0.5555555555555556, "impurity": 0.9910760598382222}, "right": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 83}, "samples": 83, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 30, "Yes": 107}, "samples": 137, "confidence": 0.781021897810219, "impurity": 0.7582971529373276}, "right": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 286}, "samples": 286, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 30, "Yes": 393}, "samples": 423, "confidence": 0.9290780141843972, "impurity": 0.369354939985463}, "class_distribution": {"No": 198, "Yes": 602}, "samples": 800, "confidence": 0.7525, "impurity": 0.8072916195433455}, "features": {"age": {"name": "age", "type": "numeric", "column_number": 0, "values": []}, "city": {"name": "city", "type": "categorical", "column_number": 10, "values": []}, "color": {"name": "color", "type": "categorical", "column_number": 8, "values": []}, "country": {"name": "country", "type": "categorical", "column_number": 11, "values": []}, "customer_id": {"name": "customer_id", "type": "numeric", "column_number": 2, "values": []}, "department": {"name": "department", "type": "categorical", "column_number": 9, "values": []}, "education": {"name": "education", "type": "categorical", "column_number": 18, "values": []}, "employment_status": {"name": "employment_status", "type": "categorical", "column_number": 17, "values": []}, "gender": {"name": "gender", "type": "categorical", "column_number": 16, "values": []}, "has_subscription": {"name": "has_subscription", "type": "categorical", "column_number": 14, "values": []}, "height_cm": {"name": "height_cm", "type": "numeric", "column_number": 3, "values": []}, "income": {"name": "income", "type": "numeric", "column_number": 1, "values": []}, "income_bracket": {"name": "income_bracket", "type": "categorical", "column_number": 21, "values": []}, "is_premium": {"name": "is_premium", "type": "numeric", "column_number": 13, "values": []}, "last_login": {"name": "last_login", "type": "categorical", "column_number": 24, "values": []}, "owns_car": {"name": "owns_car", "type": "categorical", "column_number": 15, "values": []}, "performance_grade": {"name": "performance_grade", "type": "categorical", "column_number": 22, "values": []}, "preferred_time": {"name": "preferred_time", "type": "categorical", "column_number": 25, "values": []}, "price_dollars": {"name": "price_dollars", "type": "numeric", "column_number": 7, "values": []}, "satisfaction_rating": {"name": "satisfaction_rating", "type": "numeric", "column_number": 19, "values": []}, "score_0_1": {"name": "score_0_1", "type": "numeric", "column_number": 5, "values": []}, "score_0_100": {"name": "score_0_100", "type": "numeric", "column_number": 6, "values": []}, "shirt_size": {"name": "shirt_size", "type": "categorical", "column_number": 20, "values": []}, "signup_date": {"name": "signup_date", "type": "categorical", "column_number": 23, "values": []}, "weight_kg": {"name": "weight_kg", "type": "numeric", "column_number": 4, "values": []}, "zip_code": {"name": "zip_code", "type": "numeric", "column_number": 12, "values": []}}, "features_by_index": [{"name": "age", "type": "numeric", "column_number": 0, "values": []}, {"name": "income", "type": "numeric", "column_number": 1, "values": []}, {"name": "customer_id", "type": "numeric", "column_number": 2, "values": []}, {"name": "height_cm", "type": "numeric", "column_number": 3, "values": []}, {"name": "weight_kg", "type": "numeric", "column_number": 4, "values": []}, {"name": "score_0_1", "type": "numeric", "column_number": 5, "values": []}, {"name": "score_0_100", "type": "numeric", "column_number": 6, "values": []}, {"name": "price_dollars", "type": "numeric", "column_number": 7, "values": []}, {"name": "color", "type": "categorical", "column_number": 8, "values": []}, {"name": "department", "type": "categorical", "column_number": 9, "values": []}, {"name": "city", "type": "categorical", "column_number": 10, "values": []}, {"name": "country", "type": "categorical", "column_number": 11, "values": []}, {"name": "zip_code", "type": "numeric", "column_number": 12, "values": []}, {"name": "is_premium", "type": "numeric", "column_number": 13, "values": []}, {"name": "has_subscription", "type": "categorical", "column_number": 14, "values": []}, {"name": "owns_car", "type": "categorical", "column_number": 15, "values": []}, {"name": "gender", "type": "categorical", "column_number": 16, "values": []}, {"name": "employment_status", "type": "categorical", "column_number": 17, "values": []}, {"name": "education", "type": "categorical", "column_number": 18, "values": []}, {"name": "satisfaction_rating", "type": "numeric", "column_number": 19, "values": []}, {"name": "shirt_size", "type": "categorical", "column_number": 20, "values": []}, {"name": "income_bracket", "type": "categorical", "column_number": 21, "values": []}, {"name": "performance_grade", "type": "categorical", "column_number": 22, "values": []}, {"name": "signup_date", "type": "categorical", "column_number": 23, "values": []}, {"name": "last_login", "type": "categorical", "column_number": 24, "values": []}, {"name": "preferred_time", "type": "categorical", "column_number": 25, "values": []}], "target_type": "categorical", "target_column": "y", "config": {"max_depth": 5, "min_samples": 10, "target_type": "categorical", "criterion": "entropy", "max_features": -1}, "node_count": 27, "leaf_count": 17, "depth": 5}