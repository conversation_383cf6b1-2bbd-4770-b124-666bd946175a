#!/usr/bin/env python3
"""
Standardized comparison of Python and Go decision tree implementations.
Uses identical parameters to ensure fair tree structure comparison.
"""

import json
import os
import subprocess
import sys
import tempfile
import time
from typing import Dict, Any, Tuple

# Add benchmark directory to path
sys.path.insert(0, 'benchmark')
from dt import C45DecisionTree
import pandas as pd
import yaml

def load_test_data():
    """Load test data and metadata."""
    train_df = pd.read_csv("testdata/testdata_train.csv")
    with open("testdata/testdata_metadata.yaml", 'r') as f:
        metadata = yaml.safe_load(f)
    
    print(f"Loaded {len(train_df)} training samples")
    print(f"Target distribution: {train_df['y'].value_counts().to_dict()}")
    return train_df, metadata

def run_python_with_params(train_df, max_depth=10, min_samples_split=2):
    """Run Python implementation with specified parameters."""
    print(f"\nPython C4.5 - max_depth={max_depth}, min_samples_split={min_samples_split}")
    
    start_time = time.time()
    X = train_df.drop('y', axis=1)
    y = train_df['y']
    
    # Convert min_samples_split to percentage for Python
    min_instances_pc = min_samples_split / len(train_df)
    
    tree = C45DecisionTree(
        max_depth=max_depth,
        min_instances_pc=min_instances_pc,
        enable_pruning=False,  # Disable pruning for fair comparison
        confidence_level=0.25
    )
    tree.fit(X, y)

    training_time = time.time() - start_time

    # Get tree structure using the root node's to_dict method
    if tree.root:
        tree_structure = {"root": tree.root.to_dict()}
    else:
        tree_structure = {"root": None}
    
    return {
        "execution_time": training_time,
        "tree_structure": tree_structure,
        "parameters": {
            "max_depth": max_depth,
            "min_samples_split": min_samples_split,
            "min_instances_pc": min_instances_pc
        }
    }

def run_go_with_params(max_depth=10, min_samples_split=2, min_samples_leaf=1):
    """Run Go implementation with specified parameters."""
    print(f"\nGo Mulberri - max_depth={max_depth}, min_samples_split={min_samples_split}, min_leaf={min_samples_leaf}")
    
    # Build Go binary
    build_result = subprocess.run(
        ["go", "build", "-o", "mulberri", "./cmd/mulberri"],
        capture_output=True, text=True
    )
    if build_result.returncode != 0:
        print(f"Build failed: {build_result.stderr}")
        return None
    
    start_time = time.time()
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.dt', delete=False) as tmp_file:
        output_file = tmp_file.name
    
    try:
        cmd = [
            "./mulberri", "-c", "train",
            "-i", "testdata/testdata_train.csv",
            "-t", "y", "-o", output_file,
            "--max-depth", str(max_depth),
            "--min-samples", str(min_samples_split),
            "--min-leaf", str(min_samples_leaf),
            "--criterion", "entropy"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        execution_time = time.time() - start_time
        
        if result.returncode != 0:
            print(f"Go execution failed: {result.stderr}")
            return None
        
        with open(output_file, 'r') as f:
            go_output = json.load(f)
        
        return {
            "execution_time": execution_time,
            "training": go_output,
            "parameters": {
                "max_depth": max_depth,
                "min_samples_split": min_samples_split,
                "min_samples_leaf": min_samples_leaf
            }
        }
    finally:
        if os.path.exists(output_file):
            os.unlink(output_file)

def analyze_tree_structure(tree_data, is_go_format=False):
    """Analyze tree structure and return statistics."""
    if is_go_format:
        # Go format has direct root structure
        tree = tree_data["training"]["root"]
    else:
        tree = tree_data["tree_structure"]["root"]
    
    def count_nodes(node, depth=0):
        if not isinstance(node, dict):
            return 0, 0, 0, []
        
        total_nodes = 1
        leaf_nodes = 0
        max_depth = depth
        leaf_depths = []
        
        # Check if leaf
        if is_go_format:
            is_leaf = node.get("type") == "leaf"
        else:
            is_leaf = node.get("node_type") == "leaf"
        
        if is_leaf:
            leaf_nodes = 1
            leaf_depths.append(depth)
        else:
            # Process children
            if is_go_format:
                for child_key in ["left", "right"]:
                    if child_key in node:
                        child_total, child_leaves, child_depth, child_leaf_depths = count_nodes(node[child_key], depth + 1)
                        total_nodes += child_total
                        leaf_nodes += child_leaves
                        max_depth = max(max_depth, child_depth)
                        leaf_depths.extend(child_leaf_depths)
            else:
                branches = node.get("branches", {})
                for child in branches.values():
                    child_total, child_leaves, child_depth, child_leaf_depths = count_nodes(child, depth + 1)
                    total_nodes += child_total
                    leaf_nodes += child_leaves
                    max_depth = max(max_depth, child_depth)
                    leaf_depths.extend(child_leaf_depths)
        
        return total_nodes, leaf_nodes, max_depth, leaf_depths
    
    total, leaves, depth, leaf_depths = count_nodes(tree)
    avg_leaf_depth = sum(leaf_depths) / len(leaf_depths) if leaf_depths else 0
    
    return {
        "total_nodes": total,
        "leaf_nodes": leaves,
        "decision_nodes": total - leaves,
        "max_depth": depth,
        "avg_leaf_depth": avg_leaf_depth,
        "leaf_depths": leaf_depths
    }

def extract_decision_rules(tree_data, is_go_format=False):
    """Extract decision rules from tree."""
    if is_go_format:
        # Go format has direct root structure
        tree = tree_data["training"]["root"]
    else:
        tree = tree_data["tree_structure"]["root"]
    
    rules = []
    
    def traverse(node, path="", depth=0):
        if not isinstance(node, dict):
            return
        
        # Check if leaf
        if is_go_format:
            is_leaf = node.get("type") == "leaf"
            prediction = node.get("prediction")
            samples = node.get("samples", 0)
        else:
            is_leaf = node.get("node_type") == "leaf"
            prediction = node.get("prediction") or node.get("class")
            samples = node.get("statistics", {}).get("sample_size", 0)
        
        if is_leaf:
            rules.append({
                "path": path,
                "prediction": prediction,
                "samples": samples,
                "depth": depth
            })
        else:
            # Get feature info
            feature_info = node.get("feature", {})
            feature_name = feature_info.get("name", "unknown")
            
            if is_go_format:
                threshold = node.get("threshold")
                if "left" in node:
                    traverse(node["left"], f"{path} -> {feature_name} <= {threshold}", depth + 1)
                if "right" in node:
                    traverse(node["right"], f"{path} -> {feature_name} > {threshold}", depth + 1)
            else:
                threshold = feature_info.get("threshold")
                branches = node.get("branches", {})
                for condition, child in branches.items():
                    traverse(child, f"{path} -> {feature_name} {condition} {threshold}", depth + 1)
    
    traverse(tree)
    return rules

def compare_trees(python_data, go_data):
    """Compare tree structures between implementations."""
    python_stats = analyze_tree_structure(python_data, False)
    go_stats = analyze_tree_structure(go_data, True)
    
    python_rules = extract_decision_rules(python_data, False)
    go_rules = extract_decision_rules(go_data, True)
    
    comparison = {
        "python_stats": python_stats,
        "go_stats": go_stats,
        "structural_differences": {
            "node_count_diff": abs(python_stats["total_nodes"] - go_stats["total_nodes"]),
            "leaf_count_diff": abs(python_stats["leaf_nodes"] - go_stats["leaf_nodes"]),
            "depth_diff": abs(python_stats["max_depth"] - go_stats["max_depth"]),
            "avg_depth_diff": abs(python_stats["avg_leaf_depth"] - go_stats["avg_leaf_depth"])
        },
        "decision_rules": {
            "python_rule_count": len(python_rules),
            "go_rule_count": len(go_rules),
            "python_rules": python_rules[:5],  # First 5 rules
            "go_rules": go_rules[:5]
        },
        "performance": {
            "python_time": python_data["execution_time"],
            "go_time": go_data["execution_time"],
            "speed_ratio": python_data["execution_time"] / go_data["execution_time"] if go_data["execution_time"] > 0 else "N/A"
        }
    }
    
    return comparison

def main():
    """Run standardized comparison with multiple parameter sets."""
    print("STANDARDIZED DECISION TREE COMPARISON")
    print("="*50)
    
    train_df, metadata = load_test_data()
    
    # Test with different parameter combinations
    parameter_sets = [
        {"max_depth": 10, "min_samples_split": 2, "min_samples_leaf": 1},
        {"max_depth": 5, "min_samples_split": 10, "min_samples_leaf": 5},
        {"max_depth": 15, "min_samples_split": 1, "min_samples_leaf": 1}
    ]
    
    results = {}
    
    for i, params in enumerate(parameter_sets):
        print(f"\n{'='*50}")
        print(f"PARAMETER SET {i+1}: {params}")
        print(f"{'='*50}")
        
        # Run both implementations
        python_result = run_python_with_params(
            train_df, 
            params["max_depth"], 
            params["min_samples_split"]
        )
        
        go_result = run_go_with_params(
            params["max_depth"],
            params["min_samples_split"], 
            params["min_samples_leaf"]
        )
        
        if python_result and go_result:
            comparison = compare_trees(python_result, go_result)
            results[f"params_{i+1}"] = {
                "parameters": params,
                "python_result": python_result,
                "go_result": go_result,
                "comparison": comparison
            }
            
            # Print summary
            print(f"\nRESULTS SUMMARY:")
            print(f"Python: {comparison['python_stats']['total_nodes']} nodes, {comparison['python_stats']['leaf_nodes']} leaves, depth {comparison['python_stats']['max_depth']}")
            print(f"Go:     {comparison['go_stats']['total_nodes']} nodes, {comparison['go_stats']['leaf_nodes']} leaves, depth {comparison['go_stats']['max_depth']}")
            print(f"Time:   Python {comparison['performance']['python_time']:.3f}s, Go {comparison['performance']['go_time']:.3f}s")
            print(f"Rules:  Python {comparison['decision_rules']['python_rule_count']}, Go {comparison['decision_rules']['go_rule_count']}")
    
    # Save comprehensive results
    os.makedirs("analysis_output", exist_ok=True)
    with open("analysis_output/standardized_tree_comparison.json", "w") as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n{'='*50}")
    print("COMPARISON COMPLETE")
    print(f"{'='*50}")
    print("Detailed results saved to analysis_output/standardized_tree_comparison.json")

if __name__ == "__main__":
    main()
