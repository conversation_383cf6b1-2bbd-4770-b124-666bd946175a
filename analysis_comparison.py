#!/usr/bin/env python3
"""
Comprehensive analysis script to compare Python and Go implementations
for node generation in decision tree algorithms.

This script runs both implementations with the same test data and compares:
- JSON schema structure and field naming conventions
- Timestamp and date formatting approaches
- Null value representation and handling
- Parent-child relationship modeling
- Metadata structure and content
- Serialization/deserialization behavior
- Performance characteristics
"""

import json
import os
import subprocess
import time
import sys
from pathlib import Path
from typing import Dict, Any, List, Tuple
import pandas as pd
import tempfile
import shutil

class ImplementationComparator:
    """Compares Python and Go decision tree implementations."""
    
    def __init__(self, test_data_dir: str = "testdata"):
        self.test_data_dir = Path(test_data_dir)
        self.results = {
            "python": {},
            "go": {},
            "comparison": {},
            "performance": {}
        }
        
    def setup_test_environment(self):
        """Setup test environment and validate test data."""
        print("Setting up test environment...")
        
        # Validate test data files exist
        required_files = [
            "testdata_train.csv",
            "testdata_metadata.yaml", 
            "testdata_predict.csv"
        ]
        
        for file in required_files:
            file_path = self.test_data_dir / file
            if not file_path.exists():
                raise FileNotFoundError(f"Required test file not found: {file_path}")
                
        print(f"✓ Test data validated in {self.test_data_dir}")
        
    def run_python_implementation(self) -> Dict[str, Any]:
        """Run Python C4.5 implementation and capture output."""
        print("\n=== Running Python Implementation ===")
        
        start_time = time.time()
        
        try:
            # Create temporary output file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as tmp_file:
                output_file = tmp_file.name
            
            # Run Python benchmark with JSON output
            cmd = [
                "python3", "benchmark/run_benchmark.py",
                "--datasets", "testdata",
                "--output", "analysis_output/python/",
                "--verbose",
                "--tree-stats"
            ]
            
            print(f"Executing: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=".")
            
            execution_time = time.time() - start_time
            
            if result.returncode != 0:
                print(f"Python implementation failed:")
                print(f"STDOUT: {result.stdout}")
                print(f"STDERR: {result.stderr}")
                return {"error": result.stderr, "execution_time": execution_time}
            
            # Try to extract tree structure from output
            python_result = {
                "stdout": result.stdout,
                "stderr": result.stderr,
                "execution_time": execution_time,
                "returncode": result.returncode
            }
            
            # Look for generated model files
            output_dir = Path("analysis_output/python/")
            if output_dir.exists():
                model_files = list(output_dir.glob("*.json"))
                if model_files:
                    with open(model_files[0], 'r') as f:
                        python_result["tree_structure"] = json.load(f)
            
            print(f"✓ Python implementation completed in {execution_time:.2f}s")
            return python_result
            
        except Exception as e:
            execution_time = time.time() - start_time
            print(f"✗ Python implementation failed: {e}")
            return {"error": str(e), "execution_time": execution_time}
    
    def run_go_implementation(self) -> Dict[str, Any]:
        """Run Go mulberri implementation and capture output."""
        print("\n=== Running Go Implementation ===")
        
        start_time = time.time()
        
        try:
            # Create temporary output file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.dt', delete=False) as tmp_file:
                output_file = tmp_file.name
            
            # Build Go binary first
            build_cmd = ["go", "build", "-o", "mulberri", "./cmd/mulberri"]
            print(f"Building Go binary: {' '.join(build_cmd)}")
            build_result = subprocess.run(build_cmd, capture_output=True, text=True, cwd=".")
            
            if build_result.returncode != 0:
                print(f"Go build failed: {build_result.stderr}")
                return {"error": f"Build failed: {build_result.stderr}"}
            
            # Run Go implementation
            cmd = [
                "./mulberri",
                "-c", "train",
                "-i", "testdata/testdata_train.csv",
                "-t", "y",
                "-o", output_file,
                "--verbose"
            ]
            
            print(f"Executing: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=".")
            
            execution_time = time.time() - start_time
            
            if result.returncode != 0:
                print(f"Go implementation failed:")
                print(f"STDOUT: {result.stdout}")
                print(f"STDERR: {result.stderr}")
                return {"error": result.stderr, "execution_time": execution_time}
            
            # Read the generated model file
            go_result = {
                "stdout": result.stdout,
                "stderr": result.stderr,
                "execution_time": execution_time,
                "returncode": result.returncode
            }
            
            if os.path.exists(output_file):
                with open(output_file, 'r') as f:
                    try:
                        go_result["tree_structure"] = json.load(f)
                    except json.JSONDecodeError:
                        # If not JSON, read as text
                        f.seek(0)
                        go_result["tree_structure_raw"] = f.read()
            
            print(f"✓ Go implementation completed in {execution_time:.2f}s")
            return go_result
            
        except Exception as e:
            execution_time = time.time() - start_time
            print(f"✗ Go implementation failed: {e}")
            return {"error": str(e), "execution_time": execution_time}

    def compare_json_structures(self) -> Dict[str, Any]:
        """Compare JSON structures between Python and Go implementations."""
        print("\n=== Comparing JSON Structures ===")

        comparison = {
            "schema_differences": [],
            "field_naming": [],
            "data_types": [],
            "structure_analysis": {}
        }

        python_tree = self.results["python"].get("tree_structure")
        go_tree = self.results["go"].get("tree_structure")

        if not python_tree and not go_tree:
            comparison["error"] = "No tree structures found in either implementation"
            return comparison

        # Analyze field naming conventions
        if python_tree:
            python_fields = self._extract_all_fields(python_tree)
            comparison["python_fields"] = python_fields

        if go_tree:
            go_fields = self._extract_all_fields(go_tree)
            comparison["go_fields"] = go_fields

        # Compare field naming
        if python_tree and go_tree:
            python_fields = set(self._extract_all_fields(python_tree))
            go_fields = set(self._extract_all_fields(go_tree))

            comparison["common_fields"] = list(python_fields & go_fields)
            comparison["python_only_fields"] = list(python_fields - go_fields)
            comparison["go_only_fields"] = list(go_fields - python_fields)

        # Analyze data type representations
        if python_tree:
            comparison["python_data_types"] = self._analyze_data_types(python_tree)
        if go_tree:
            comparison["go_data_types"] = self._analyze_data_types(go_tree)

        return comparison

    def _extract_all_fields(self, obj, fields=None) -> List[str]:
        """Recursively extract all field names from a nested object."""
        if fields is None:
            fields = []

        if isinstance(obj, dict):
            for key, value in obj.items():
                if key not in fields:
                    fields.append(key)
                self._extract_all_fields(value, fields)
        elif isinstance(obj, list):
            for item in obj:
                self._extract_all_fields(item, fields)

        return fields

    def _analyze_data_types(self, obj, path="root") -> Dict[str, str]:
        """Analyze data types in the tree structure."""
        type_analysis = {}

        if isinstance(obj, dict):
            for key, value in obj.items():
                current_path = f"{path}.{key}"
                type_analysis[current_path] = type(value).__name__

                # Special handling for datetime fields
                if isinstance(value, str) and any(dt_indicator in key.lower()
                    for dt_indicator in ['date', 'time', 'timestamp']):
                    type_analysis[f"{current_path}_format"] = self._detect_datetime_format(value)

                if isinstance(value, (dict, list)):
                    type_analysis.update(self._analyze_data_types(value, current_path))
        elif isinstance(obj, list) and obj:
            type_analysis[f"{path}_list_type"] = type(obj[0]).__name__
            if isinstance(obj[0], (dict, list)):
                type_analysis.update(self._analyze_data_types(obj[0], f"{path}[0]"))

        return type_analysis

    def _detect_datetime_format(self, value: str) -> str:
        """Detect datetime format from string value."""
        if 'T' in value and 'Z' in value:
            return "ISO8601_UTC"
        elif 'T' in value:
            return "ISO8601"
        elif ':' in value and len(value.split(':')) == 3:
            return "TIME_ONLY"
        else:
            return "UNKNOWN"

    def analyze_performance(self) -> Dict[str, Any]:
        """Analyze performance characteristics of both implementations."""
        print("\n=== Analyzing Performance ===")

        performance = {
            "execution_times": {},
            "memory_analysis": {},
            "output_sizes": {}
        }

        # Execution times
        if "execution_time" in self.results["python"]:
            performance["execution_times"]["python"] = self.results["python"]["execution_time"]
        if "execution_time" in self.results["go"]:
            performance["execution_times"]["go"] = self.results["go"]["execution_time"]

        # Calculate relative performance
        if all(key in performance["execution_times"] for key in ["python", "go"]):
            python_time = performance["execution_times"]["python"]
            go_time = performance["execution_times"]["go"]

            if go_time > 0:
                performance["speed_ratio"] = python_time / go_time
                performance["faster_implementation"] = "Go" if go_time < python_time else "Python"

        # Analyze output sizes
        python_tree = self.results["python"].get("tree_structure")
        go_tree = self.results["go"].get("tree_structure")

        if python_tree:
            performance["output_sizes"]["python_json_size"] = len(json.dumps(python_tree))
            performance["output_sizes"]["python_node_count"] = self._count_nodes(python_tree)

        if go_tree:
            performance["output_sizes"]["go_json_size"] = len(json.dumps(go_tree))
            performance["output_sizes"]["go_node_count"] = self._count_nodes(go_tree)

        return performance

    def _count_nodes(self, tree_obj) -> int:
        """Count total nodes in tree structure."""
        if not isinstance(tree_obj, dict):
            return 0

        count = 1  # Current node

        # Look for common child node patterns
        for key, value in tree_obj.items():
            if key in ['left', 'right', 'children', 'branches'] and isinstance(value, dict):
                count += self._count_nodes(value)
            elif key in ['children', 'branches'] and isinstance(value, list):
                for child in value:
                    if isinstance(child, dict):
                        count += self._count_nodes(child)

        return count

    def generate_comparison_report(self):
        """Generate comprehensive comparison report."""
        print("\n=== Generating Comparison Report ===")

        os.makedirs("analysis_output", exist_ok=True)

        report_lines = []
        report_lines.append("# Decision Tree Implementation Comparison Report")
        report_lines.append("=" * 60)
        report_lines.append("")

        # Executive Summary
        report_lines.append("## Executive Summary")
        report_lines.append("")

        python_success = "error" not in self.results["python"]
        go_success = "error" not in self.results["go"]

        report_lines.append(f"- Python Implementation: {'✓ SUCCESS' if python_success else '✗ FAILED'}")
        report_lines.append(f"- Go Implementation: {'✓ SUCCESS' if go_success else '✗ FAILED'}")
        report_lines.append("")

        # Performance Analysis
        if "performance" in self.results:
            perf = self.results["performance"]
            report_lines.append("## Performance Comparison")
            report_lines.append("")

            if "execution_times" in perf:
                times = perf["execution_times"]
                report_lines.append("### Execution Times")
                for impl, time_val in times.items():
                    report_lines.append(f"- {impl.title()}: {time_val:.3f} seconds")

                if "faster_implementation" in perf:
                    report_lines.append(f"- Faster Implementation: {perf['faster_implementation']}")
                    if "speed_ratio" in perf:
                        report_lines.append(f"- Speed Ratio: {perf['speed_ratio']:.2f}x")
                report_lines.append("")

            if "output_sizes" in perf:
                sizes = perf["output_sizes"]
                report_lines.append("### Output Analysis")
                for key, value in sizes.items():
                    report_lines.append(f"- {key.replace('_', ' ').title()}: {value}")
                report_lines.append("")

        # Structure Comparison
        if "comparison" in self.results:
            comp = self.results["comparison"]
            report_lines.append("## JSON Structure Comparison")
            report_lines.append("")

            if "common_fields" in comp:
                report_lines.append("### Common Fields")
                for field in comp["common_fields"]:
                    report_lines.append(f"- {field}")
                report_lines.append("")

            if "python_only_fields" in comp:
                report_lines.append("### Python-Only Fields")
                for field in comp["python_only_fields"]:
                    report_lines.append(f"- {field}")
                report_lines.append("")

            if "go_only_fields" in comp:
                report_lines.append("### Go-Only Fields")
                for field in comp["go_only_fields"]:
                    report_lines.append(f"- {field}")
                report_lines.append("")

        # Error Analysis
        report_lines.append("## Error Analysis")
        report_lines.append("")

        if not python_success:
            report_lines.append("### Python Implementation Errors")
            report_lines.append(f"```")
            report_lines.append(str(self.results["python"].get("error", "Unknown error")))
            report_lines.append(f"```")
            report_lines.append("")

        if not go_success:
            report_lines.append("### Go Implementation Errors")
            report_lines.append(f"```")
            report_lines.append(str(self.results["go"].get("error", "Unknown error")))
            report_lines.append(f"```")
            report_lines.append("")

        # Recommendations
        report_lines.append("## Recommendations")
        report_lines.append("")
        report_lines.append("### Standardization Opportunities")

        if python_success and go_success:
            report_lines.append("- Both implementations executed successfully")
            report_lines.append("- Consider standardizing field naming conventions")
            report_lines.append("- Evaluate performance trade-offs for production use")
        else:
            report_lines.append("- Fix implementation errors before comparison")
            report_lines.append("- Ensure both implementations use same input format")

        report_lines.append("")
        report_lines.append("### Next Steps")
        report_lines.append("- Detailed schema mapping for interoperability")
        report_lines.append("- Performance optimization based on benchmarks")
        report_lines.append("- Integration testing with real-world datasets")

        # Write report
        with open("analysis_output/comparison_report.md", "w") as f:
            f.write("\n".join(report_lines))

        print("✓ Comparison report generated: analysis_output/comparison_report.md")

if __name__ == "__main__":
    comparator = ImplementationComparator()
    
    try:
        # Setup environment
        comparator.setup_test_environment()
        
        # Run both implementations
        print("\n" + "="*60)
        print("DECISION TREE IMPLEMENTATION COMPARISON")
        print("="*60)
        
        # Run implementations
        python_results = comparator.run_python_implementation()
        go_results = comparator.run_go_implementation()

        # Store results
        comparator.results["python"] = python_results
        comparator.results["go"] = go_results

        # Perform comparisons
        comparator.results["comparison"] = comparator.compare_json_structures()
        comparator.results["performance"] = comparator.analyze_performance()

        # Generate comprehensive report
        comparator.generate_comparison_report()

        # Save raw results
        os.makedirs("analysis_output", exist_ok=True)
        with open("analysis_output/raw_comparison_results.json", "w") as f:
            json.dump(comparator.results, f, indent=2, default=str)

        print(f"\n✓ Analysis complete. Results saved to analysis_output/")
        
    except Exception as e:
        print(f"Analysis failed: {e}")
        sys.exit(1)
