#!/usr/bin/env python3
"""
Direct test of Python C4.5 implementation using testdata.
"""

import json
import os
import sys
import time
from pathlib import Path

# Add benchmark directory to path
sys.path.insert(0, 'benchmark')

from dt import C45DecisionTree, DataType, Feature, NumericFeature, NominalFeature
import pandas as pd
import yaml

def load_test_data():
    """Load test data and metadata."""
    print("Loading test data...")
    
    # Load training data
    train_df = pd.read_csv("testdata/testdata_train.csv")
    print(f"Loaded {len(train_df)} training samples")
    
    # Load metadata
    with open("testdata/testdata_metadata.yaml", 'r') as f:
        metadata = yaml.safe_load(f)
    
    return train_df, metadata

def create_features_from_metadata(metadata):
    """Create feature objects from metadata."""
    features = []
    
    for feature_name, feature_info in metadata.items():
        if feature_name == 'y':  # Skip target variable
            continue
            
        feature_type = feature_info['type']
        
        if feature_type == 'numeric':
            features.append(NumericFeature(feature_name, 0.0))  # Threshold will be determined during training
        elif feature_type == 'nominal':
            features.append(NominalFeature(feature_name, feature_info['values']))
        elif feature_type == 'datetime':
            # Treat datetime as numeric for now (could be enhanced)
            features.append(NumericFeature(feature_name, 0.0))
    
    return features

def train_python_tree(train_df, features):
    """Train Python C4.5 decision tree."""
    print("Training Python C4.5 decision tree...")
    
    start_time = time.time()
    
    # Prepare data
    X = train_df.drop('y', axis=1)
    y = train_df['y']
    
    # Create and train tree
    tree = C45DecisionTree()
    tree.fit(X, y)
    
    training_time = time.time() - start_time
    
    print(f"Training completed in {training_time:.3f} seconds")
    
    return tree, training_time

def serialize_tree_structure(tree):
    """Serialize tree structure to JSON-compatible format."""
    
    def serialize_node(node):
        """Recursively serialize a node."""
        if node is None:
            return None
        
        node_data = {
            "node_type": node.node_type.value if hasattr(node.node_type, 'value') else str(node.node_type),
            "is_leaf": node.is_leaf()
        }
        
        # Add statistics if available
        if hasattr(node, 'statistics') and node.statistics:
            node_data["statistics"] = {
                "sample_size": node.statistics.sample_size,
                "node_entropy": node.statistics.node_entropy,
                "node_depth": node.statistics.node_depth,
                "class_distribution": node.statistics.class_distribution
            }
            if hasattr(node.statistics, 'gain_ratio') and node.statistics.gain_ratio is not None:
                node_data["statistics"]["gain_ratio"] = node.statistics.gain_ratio
            if hasattr(node.statistics, 'error_rate') and node.statistics.error_rate is not None:
                node_data["statistics"]["error_rate"] = node.statistics.error_rate
        
        if node.is_leaf():
            # Leaf node
            node_data["prediction"] = node.majority_class
            node_data["class"] = node.majority_class
        else:
            # Branch node
            node_data["feature"] = {
                "name": node.feature.name,
                "data_type": node.feature.data_type.value if hasattr(node.feature.data_type, 'value') else str(node.feature.data_type)
            }
            
            if hasattr(node.feature, 'threshold'):
                node_data["feature"]["threshold"] = node.feature.threshold
            if hasattr(node.feature, 'categories'):
                node_data["feature"]["categories"] = node.feature.categories
            
            node_data["majority_class"] = node.majority_class
            
            # Serialize branches
            node_data["branches"] = {}
            for branch_key, child_node in node.branches.items():
                node_data["branches"][branch_key] = serialize_node(child_node)
        
        return node_data
    
    if not hasattr(tree, 'root') or tree.root is None:
        return {"error": "No root node found"}
    
    return {
        "root": serialize_node(tree.root),
        "metadata": {
            "algorithm": "C4.5",
            "implementation": "Python",
            "features_count": len(tree.root.branches) if hasattr(tree.root, 'branches') else 0
        }
    }

def main():
    """Main execution function."""
    print("="*60)
    print("PYTHON C4.5 IMPLEMENTATION TEST")
    print("="*60)
    
    try:
        # Load data
        train_df, metadata = load_test_data()
        
        # Create features
        features = create_features_from_metadata(metadata)
        print(f"Created {len(features)} features")
        
        # Train tree
        tree, training_time = train_python_tree(train_df, features)
        
        # Serialize tree structure
        tree_structure = serialize_tree_structure(tree)
        
        # Create output directory
        os.makedirs("analysis_output/python", exist_ok=True)
        
        # Save results
        results = {
            "execution_time": training_time,
            "tree_structure": tree_structure,
            "metadata": {
                "training_samples": len(train_df),
                "features_count": len(features),
                "target_classes": train_df['y'].unique().tolist()
            }
        }
        
        with open("analysis_output/python/tree_structure.json", "w") as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"✓ Results saved to analysis_output/python/tree_structure.json")
        print(f"✓ Training time: {training_time:.3f} seconds")
        
        return results
        
    except Exception as e:
        print(f"✗ Python implementation failed: {e}")
        import traceback
        traceback.print_exc()
        return {"error": str(e)}

if __name__ == "__main__":
    main()
