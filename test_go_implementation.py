#!/usr/bin/env python3
"""
Test script for Go mulberri implementation using testdata.
"""

import json
import os
import subprocess
import time
import tempfile
from pathlib import Path

def build_go_binary():
    """Build the Go mulberri binary."""
    print("Building Go mulberri binary...")
    
    build_cmd = ["go", "build", "-o", "mulberri", "./cmd/mulberri"]
    result = subprocess.run(build_cmd, capture_output=True, text=True, cwd=".")
    
    if result.returncode != 0:
        print(f"✗ Go build failed:")
        print(f"STDERR: {result.stderr}")
        return False
    
    print("✓ Go binary built successfully")
    return True

def run_go_training():
    """Run Go implementation for training."""
    print("Running Go mulberri training...")
    
    start_time = time.time()
    
    # Create temporary output file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.dt', delete=False) as tmp_file:
        output_file = tmp_file.name
    
    try:
        # Run Go implementation
        cmd = [
            "./mulberri",
            "-c", "train",
            "-i", "testdata/testdata_train.csv",
            "-t", "y",
            "-o", output_file,
            "--verbose"
        ]
        
        print(f"Executing: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=".")
        
        execution_time = time.time() - start_time
        
        if result.returncode != 0:
            print(f"✗ Go implementation failed:")
            print(f"STDOUT: {result.stdout}")
            print(f"STDERR: {result.stderr}")
            return {
                "error": result.stderr,
                "execution_time": execution_time,
                "stdout": result.stdout,
                "returncode": result.returncode
            }
        
        # Read the generated model file
        go_result = {
            "stdout": result.stdout,
            "stderr": result.stderr,
            "execution_time": execution_time,
            "returncode": result.returncode
        }
        
        if os.path.exists(output_file):
            with open(output_file, 'r') as f:
                try:
                    go_result["tree_structure"] = json.load(f)
                except json.JSONDecodeError as e:
                    # If not JSON, read as text
                    f.seek(0)
                    content = f.read()
                    go_result["tree_structure_raw"] = content
                    go_result["json_parse_error"] = str(e)
        else:
            go_result["error"] = f"Output file not created: {output_file}"
        
        print(f"✓ Go implementation completed in {execution_time:.3f} seconds")
        return go_result
        
    except Exception as e:
        execution_time = time.time() - start_time
        print(f"✗ Go implementation failed: {e}")
        return {"error": str(e), "execution_time": execution_time}
    
    finally:
        # Clean up temporary file
        if os.path.exists(output_file):
            os.unlink(output_file)

def test_go_prediction():
    """Test Go implementation for prediction."""
    print("Testing Go mulberri prediction...")
    
    # First, we need a trained model
    with tempfile.NamedTemporaryFile(mode='w', suffix='.dt', delete=False) as model_file:
        model_path = model_file.name
    
    try:
        # Train a model first
        train_cmd = [
            "./mulberri",
            "-c", "train",
            "-i", "testdata/testdata_train.csv",
            "-t", "y",
            "-o", model_path
        ]
        
        train_result = subprocess.run(train_cmd, capture_output=True, text=True, cwd=".")
        
        if train_result.returncode != 0:
            return {"error": f"Training failed: {train_result.stderr}"}
        
        # Now test prediction
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as pred_file:
            pred_output = pred_file.name
        
        predict_cmd = [
            "./mulberri",
            "-c", "predict",
            "-i", "testdata/testdata_predict.csv",
            "-m", model_path,
            "-o", pred_output,
            "--verbose"
        ]
        
        start_time = time.time()
        pred_result = subprocess.run(predict_cmd, capture_output=True, text=True, cwd=".")
        prediction_time = time.time() - start_time
        
        result = {
            "prediction_time": prediction_time,
            "stdout": pred_result.stdout,
            "stderr": pred_result.stderr,
            "returncode": pred_result.returncode
        }
        
        if pred_result.returncode == 0 and os.path.exists(pred_output):
            with open(pred_output, 'r') as f:
                result["predictions"] = f.read()
            print(f"✓ Prediction completed in {prediction_time:.3f} seconds")
        else:
            result["error"] = pred_result.stderr
            print(f"✗ Prediction failed: {pred_result.stderr}")
        
        return result
        
    finally:
        # Clean up temporary files
        for temp_path in [model_path, pred_output]:
            if os.path.exists(temp_path):
                os.unlink(temp_path)

def analyze_go_output(go_result):
    """Analyze Go implementation output structure."""
    analysis = {
        "execution_successful": "error" not in go_result,
        "has_tree_structure": "tree_structure" in go_result,
        "output_format": "unknown"
    }
    
    if "tree_structure" in go_result:
        tree = go_result["tree_structure"]
        analysis["output_format"] = "json"
        analysis["tree_fields"] = list(tree.keys()) if isinstance(tree, dict) else []
        
        # Analyze tree structure
        if isinstance(tree, dict):
            analysis["tree_analysis"] = {
                "root_node_present": "root" in tree or any(key in tree for key in ["Type", "type", "node_type"]),
                "has_metadata": any(key in tree for key in ["metadata", "info", "config"]),
                "field_count": len(tree)
            }
    
    elif "tree_structure_raw" in go_result:
        analysis["output_format"] = "text"
        analysis["raw_content_length"] = len(go_result["tree_structure_raw"])
    
    return analysis

def main():
    """Main execution function."""
    print("="*60)
    print("GO MULBERRI IMPLEMENTATION TEST")
    print("="*60)
    
    # Build Go binary
    if not build_go_binary():
        return {"error": "Failed to build Go binary"}
    
    # Run training test
    training_result = run_go_training()
    
    # Run prediction test
    prediction_result = test_go_prediction()
    
    # Analyze results
    training_analysis = analyze_go_output(training_result)
    
    # Combine results
    results = {
        "training": training_result,
        "prediction": prediction_result,
        "analysis": training_analysis,
        "metadata": {
            "test_timestamp": time.time(),
            "implementation": "Go mulberri"
        }
    }
    
    # Save results
    os.makedirs("analysis_output/go", exist_ok=True)
    with open("analysis_output/go/test_results.json", "w") as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n✓ Go implementation test complete")
    print(f"✓ Results saved to analysis_output/go/test_results.json")
    
    # Print summary
    print(f"\n--- SUMMARY ---")
    print(f"Training: {'✓ SUCCESS' if training_analysis['execution_successful'] else '✗ FAILED'}")
    if 'execution_time' in training_result:
        print(f"Training time: {training_result['execution_time']:.3f} seconds")
    
    return results

if __name__ == "__main__":
    main()
