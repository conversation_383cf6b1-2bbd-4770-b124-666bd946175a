#!/usr/bin/env python3
"""
Comprehensive comparison of Python and Go decision tree implementations.
"""

import json
import os
from typing import Dict, Any, List, Set
from pathlib import Path

def load_results():
    """Load results from both implementations."""
    python_file = "analysis_output/python/tree_structure.json"
    go_file = "analysis_output/go/test_results.json"
    
    with open(python_file, 'r') as f:
        python_data = json.load(f)
    
    with open(go_file, 'r') as f:
        go_data = json.load(f)
    
    return python_data, go_data

def extract_field_names(obj, prefix="", fields=None):
    """Recursively extract all field names from nested object."""
    if fields is None:
        fields = set()
    
    if isinstance(obj, dict):
        for key, value in obj.items():
            full_key = f"{prefix}.{key}" if prefix else key
            fields.add(full_key)
            if isinstance(value, (dict, list)):
                extract_field_names(value, full_key, fields)
    elif isinstance(obj, list) and obj:
        if isinstance(obj[0], (dict, list)):
            extract_field_names(obj[0], f"{prefix}[0]", fields)
    
    return fields

def analyze_node_structure(tree_data, implementation_name):
    """Analyze the structure of tree nodes."""
    analysis = {
        "implementation": implementation_name,
        "node_types": set(),
        "field_patterns": set(),
        "data_type_representations": set(),
        "branching_patterns": set()
    }
    
    def analyze_node(node, path="root"):
        if not isinstance(node, dict):
            return
        
        # Node type analysis
        if "node_type" in node:
            analysis["node_types"].add(node["node_type"])
        elif "type" in node:
            analysis["node_types"].add(node["type"])
        
        # Field pattern analysis
        for key in node.keys():
            analysis["field_patterns"].add(key)
        
        # Data type representation
        if "feature" in node and isinstance(node["feature"], dict):
            feature = node["feature"]
            if "data_type" in feature:
                analysis["data_type_representations"].add(feature["data_type"])
            elif "type" in feature:
                analysis["data_type_representations"].add(feature["type"])
        
        # Branching patterns
        if "branches" in node:
            analysis["branching_patterns"].add("branches_dict")
            for branch_key, child in node["branches"].items():
                analyze_node(child, f"{path}.branches.{branch_key}")
        elif "left" in node and "right" in node:
            analysis["branching_patterns"].add("left_right")
            analyze_node(node["left"], f"{path}.left")
            analyze_node(node["right"], f"{path}.right")
    
    # Start analysis from root
    if "tree_structure" in tree_data:
        if "root" in tree_data["tree_structure"]:
            analyze_node(tree_data["tree_structure"]["root"])
        else:
            analyze_node(tree_data["tree_structure"])
    
    # Convert sets to lists for JSON serialization
    for key in analysis:
        if isinstance(analysis[key], set):
            analysis[key] = list(analysis[key])
    
    return analysis

def compare_performance(python_data, go_data):
    """Compare performance metrics."""
    comparison = {}
    
    # Execution times
    python_time = python_data.get("execution_time", 0)
    go_time = go_data.get("training", {}).get("execution_time", 0)
    
    comparison["execution_times"] = {
        "python_seconds": python_time,
        "go_seconds": go_time,
        "speed_ratio": python_time / go_time if go_time > 0 else "N/A",
        "faster_implementation": "Go" if go_time < python_time else "Python"
    }
    
    # Tree structure sizes
    python_tree = python_data.get("tree_structure", {})
    go_tree = go_data.get("training", {}).get("tree_structure", {})
    
    comparison["output_sizes"] = {
        "python_json_chars": len(json.dumps(python_tree)),
        "go_json_chars": len(json.dumps(go_tree)),
        "python_fields": len(extract_field_names(python_tree)),
        "go_fields": len(extract_field_names(go_tree))
    }
    
    return comparison

def compare_field_naming():
    """Compare field naming conventions between implementations."""
    
    # Key field mappings identified from the structures
    field_mappings = {
        "Node Type": {
            "python": "node_type",
            "go": "type",
            "values": {
                "python": ["branch", "leaf"],
                "go": ["decision", "leaf"]
            }
        },
        "Feature Information": {
            "python": "feature.name, feature.data_type, feature.threshold",
            "go": "feature.name, feature.type, threshold",
            "notes": "Go stores threshold at node level, Python at feature level"
        },
        "Statistics": {
            "python": "statistics.sample_size, statistics.node_entropy, statistics.class_distribution",
            "go": "samples, impurity, class_distribution",
            "notes": "Different field names and nesting levels"
        },
        "Branching": {
            "python": "branches (dict with string keys like '<=', '>')",
            "go": "left, right (binary tree structure)",
            "notes": "Fundamentally different branching approaches"
        },
        "Leaf Nodes": {
            "python": "prediction, class, majority_class",
            "go": "prediction, class_distribution, confidence",
            "notes": "Similar information, different field names"
        }
    }
    
    return field_mappings

def generate_compatibility_analysis():
    """Generate analysis of compatibility requirements."""
    
    compatibility_issues = [
        {
            "category": "Schema Structure",
            "issue": "Different root structure",
            "python": "tree_structure.root",
            "go": "root",
            "impact": "High - requires wrapper/unwrapper",
            "solution": "Create schema adapter layer"
        },
        {
            "category": "Node Types",
            "issue": "Different node type values",
            "python": "branch/leaf",
            "go": "decision/leaf", 
            "impact": "Medium - requires value mapping",
            "solution": "Create enum mapping dictionary"
        },
        {
            "category": "Branching Model",
            "issue": "Different branching approaches",
            "python": "branches dict with condition strings",
            "go": "left/right binary tree",
            "impact": "High - fundamental structural difference",
            "solution": "Complex transformation logic needed"
        },
        {
            "category": "Feature Representation",
            "issue": "Different feature field organization",
            "python": "feature.data_type, feature.threshold",
            "go": "feature.type, threshold (at node level)",
            "impact": "Medium - requires field reorganization",
            "solution": "Field mapping and restructuring"
        },
        {
            "category": "Statistics",
            "issue": "Different statistical field names",
            "python": "sample_size, node_entropy, node_depth",
            "go": "samples, impurity, confidence",
            "impact": "Medium - requires field renaming",
            "solution": "Statistical field mapping"
        },
        {
            "category": "Data Types",
            "issue": "Different type naming",
            "python": "numeric/nominal",
            "go": "numeric/categorical",
            "impact": "Low - simple string replacement",
            "solution": "Type name mapping"
        }
    ]
    
    return compatibility_issues

def main():
    """Main comparison function."""
    print("="*60)
    print("DECISION TREE IMPLEMENTATION COMPARISON")
    print("="*60)
    
    # Load data
    python_data, go_data = load_results()
    
    # Analyze structures
    python_analysis = analyze_node_structure(python_data, "Python")
    go_analysis = analyze_node_structure(go_data["training"], "Go")
    
    # Compare performance
    performance_comparison = compare_performance(python_data, go_data)
    
    # Field naming comparison
    field_mappings = compare_field_naming()
    
    # Compatibility analysis
    compatibility_issues = generate_compatibility_analysis()
    
    # Compile comprehensive report
    report = {
        "summary": {
            "python_execution_time": python_data.get("execution_time", 0),
            "go_execution_time": go_data.get("training", {}).get("execution_time", 0),
            "speed_advantage": "Go" if go_data.get("training", {}).get("execution_time", 0) < python_data.get("execution_time", 0) else "Python"
        },
        "structural_analysis": {
            "python": python_analysis,
            "go": go_analysis
        },
        "performance_comparison": performance_comparison,
        "field_mappings": field_mappings,
        "compatibility_issues": compatibility_issues,
        "recommendations": {
            "immediate_actions": [
                "Create schema transformation layer",
                "Implement field mapping dictionaries",
                "Design branching model converter"
            ],
            "architectural_decisions": [
                "Choose canonical schema format",
                "Decide on branching model standard",
                "Establish field naming conventions"
            ],
            "integration_strategy": [
                "Build bidirectional converters",
                "Create validation layer",
                "Implement compatibility tests"
            ]
        }
    }
    
    # Save comprehensive report
    os.makedirs("analysis_output", exist_ok=True)
    with open("analysis_output/comprehensive_comparison.json", "w") as f:
        json.dump(report, f, indent=2, default=str)
    
    # Print summary
    print(f"\n--- PERFORMANCE SUMMARY ---")
    print(f"Python execution time: {python_data.get('execution_time', 0):.3f} seconds")
    print(f"Go execution time: {go_data.get('training', {}).get('execution_time', 0):.3f} seconds")
    print(f"Speed advantage: {report['summary']['speed_advantage']}")
    
    print(f"\n--- STRUCTURAL DIFFERENCES ---")
    print(f"Python node types: {python_analysis['node_types']}")
    print(f"Go node types: {go_analysis['node_types']}")
    print(f"Python branching: {python_analysis['branching_patterns']}")
    print(f"Go branching: {go_analysis['branching_patterns']}")
    
    print(f"\n--- COMPATIBILITY ISSUES ---")
    for issue in compatibility_issues[:3]:  # Show top 3 issues
        print(f"• {issue['category']}: {issue['issue']} (Impact: {issue['impact']})")
    
    print(f"\n✓ Comprehensive comparison saved to analysis_output/comprehensive_comparison.json")

if __name__ == "__main__":
    main()
